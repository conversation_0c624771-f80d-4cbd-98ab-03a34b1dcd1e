@file:Suppress("MagicNumber")

package com.superhexa.supervision.feature.alipay.model

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.alipay.AlipaySDKManager
import com.superhexa.supervision.feature.alipay.R
import com.superhexa.supervision.feature.alipay.data.GuideState
import com.superhexa.supervision.feature.alipay.data.PageContent
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.miwear.speechhub.presentation.translate.observer.RecordObserver
import com.superhexa.supervision.feature.miwearglasses.BuildConfig
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.engine.process.TtsDependency
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

class AliPayGuideViewModel(application: Application) : AndroidViewModel(application) {
    private var hasShownDialog = false
    private var isConnect = false
    private var isWear = false
    private var recordObserver: RecordObserver? = null
    val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private val decorator by lazy {
        if (DeviceModelManager.isMijiaO95SeriesDevice(bondDevice?.model)) {
            DecoratorUtil.getDecorator<O95StateLiveData>(bondDevice)
        } else {
            null
        }
    }
    val deviceStateLiveData by lazy { decorator?.liveData }

    // 对话框事件
    private val _showDialogEvent = MutableLiveData<Event<String>>()
    val showDialogEvent: LiveData<Event<String>> = _showDialogEvent
    private val appContext = application.applicationContext
    private val _guideState = MutableStateFlow<GuideState>(GuideState.actionPre)
    val guideState: StateFlow<GuideState> = _guideState
    private val _isAliPayBind = MutableStateFlow(false)
    val isAliPayBind: StateFlow<Boolean> = _isAliPayBind
    val bindingState = AlipaySDKManager.INSTANCE.bindingState

    companion object {
        private const val TAG = "AliPayGuideViewModel"
        private const val DELAY_TIME = 1000L
    }

    fun initSteps() {
        Timber.i("initSteps")
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                delay(DELAY_TIME)
            }
            _guideState.collect { state ->
                Timber.i("received state:$state")
                if (!getWearStates() || !getConnectStates()) {
                    Timber.i("collect ${getWearStates()},${getConnectStates()}")
                    return@collect
                }
                when (state) {
                    GuideState.actionPre -> {
                        startAction("当你选好商品，准备用眼镜付款") {
                            updateSteps(GuideState.actionStartVoice)
                        }
                    }

                    GuideState.actionStartVoice -> {
                        startAction("试着说，小爱同学，支付0.01元") {}
                    }

                    GuideState.actionStartScan -> {
                    }

                    GuideState.actionScanSucess -> {
                    }

                    GuideState.Finished -> {
                        _navigateToFinish.value = Event(Unit)
                    }

                    GuideState.dialog -> {}
                }
            }
        }
    }

    fun checkBindStatus() {
        viewModelScope.launch(Dispatchers.IO) {
            val status = AlipaySDKManager.INSTANCE.getBindStatus(true)
            withContext(Dispatchers.Main) {
                _isAliPayBind.value = status
                Timber.d("update bindStatus $status,${isAliPayBind.value}")
            }
        }
    }

    private fun startAction(tts: String, function: () -> Unit) {
        val id = AiSpeechEngine.INSTANCE.startTts(tts)
        id?.let {
            TtsDependency.onReceiveTtsSpeech(id)
            TtsDependency.processDependencyTask(id) {
                viewModelScope.launch {
                    Timber.i("startAction onTtsPlayDone")
                    TtsDependency.resetTtsTtsSpeechFlag("onTtsPlayDone")
                    withContext(Dispatchers.IO) {
                        delay(DELAY_TIME)
                    }
                    function.invoke()
                }
            }
        }
    }

    val currentIndex: StateFlow<Int> = _guideState.map { state ->
        Timber.i("received state:$state")
        when (state) {
            GuideState.actionPre -> 0
            GuideState.actionStartVoice -> 1
            GuideState.actionStartScan -> 2
            GuideState.actionScanSucess -> 3
            GuideState.Finished -> 3
            GuideState.dialog -> 0
        }
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), 0)

    // 页面数据源
    val pages: List<PageContent> by lazy {
        // 可从资源文件/网络加载数据
        listOf(
            PageContent(
                title = null,
                mainText = appContext.getString(R.string.text_alipay_selling_points_item_first_title),
                subTitle = appContext.getString(R.string.text_alipay_selling_points_item_desc),
                imageId = null
            ),
            PageContent(
                title = appContext.getString(R.string.text_alipay_selling_points_item_little_title),
                mainText = appContext.getString(R.string.text_alipay_selling_points_item_second_title),
                subTitle = appContext.getString(R.string.text_alipay_selling_points_item_desc),
                imageId = null
            ),
            PageContent(
                title = null,
                mainText = appContext.getString(R.string.text_alipay_selling_points_item_third_title),
                subTitle = appContext.getString(R.string.text_alipay_selling_points_item_desc),
                imageId = R.mipmap.ic_guide_ai_speech_item_alipay_code

            ),
            PageContent(
                title = appContext.getString(R.string.text_alipay_selling_points_item_little_title),
                mainText = appContext.getString(R.string.text_alipay_selling_points_item_fourth_title),
                subTitle = appContext.getString(R.string.text_alipay_selling_points_item_desc),
                imageId = null
            )
        )
    }

    // 导航事件
    private val _navigateToFinish = MutableLiveData<Event<Unit>>()
    val navigateToFinish: LiveData<Event<Unit>> = _navigateToFinish

    // 设备状态检查逻辑（示例）
    private fun checkDeviceConnection(isConnected: Boolean) {
        Timber.i("checkDeviceConnection $isConnected,$hasShownDialog")
        if (!isConnected && !hasShownDialog) {
            // 触发对话框事件
            _showDialogEvent.value = Event("设备断开连接，请重新连接")
        }
    }

    private fun checkDeviceWearStatus(isWear: Boolean) {
        Timber.i("checkDeviceWearStatus $isWear,$hasShownDialog")
        if (!isWear && !hasShownDialog) {
            // 触发对话框事件
            _showDialogEvent.value = Event("设备未佩戴，请佩戴眼镜")
        }
    }

    fun restartGuide(reason: String) {
        Timber.i("restartGuide $reason")
        updateSteps(GuideState.actionPre)
    }

    fun updateSteps(state: GuideState) {
        Timber.i("updateSteps:$state")
        _guideState.value = state
    }

    private fun updateConnectState(isConnect: Boolean) {
        Timber.i("updateConnectState:$isConnect")
        this.isConnect = isConnect
    }

    fun getConnectStates(): Boolean {
        Timber.i("getConnectStates:$isConnect")
        return isConnect
    }

    private fun updateWearState(isWear: Boolean) {
        Timber.i("updateWearState:$isWear")
        this.isWear = isWear
    }

    fun getWearStates(): Boolean {
        Timber.i("getWearStates:$isWear")
        return isWear
    }

    fun updateDialogStatus(isShowing: Boolean) {
        hasShownDialog = isShowing
        if (isShowing) {
            updateSteps(GuideState.dialog)
        }
        Timber.i("updateDialogStatus:$hasShownDialog")
    }

    fun addRecordObserver(lifecycleOwner: LifecycleOwner) {
        Timber.i("addRecordStateObserver")
        recordObserver = RecordObserver(lifecycleOwner)
        recordObserver?.addRecordObserve(
            decorator = decorator,
            onDeviceConnectState = { isConnect ->
                Timber.i("onDeviceConnectState:$isConnect")
                updateConnectState(isConnect)
                checkDeviceConnection(isConnect)
            },
            onCameraJointState = { isIn ->
                Timber.i("onCameraJointState:$isIn")
            },
            onDeviceWearingState = { isWear ->
                Timber.i("onDeviceWearingState:$isWear")
                updateWearState(isWear)
                checkDeviceWearStatus(isWear)
            },
            onRecordState = { status ->
                Timber.i("onRecordState:$status")
            }
        )
    }

    fun isSupportAlipay(): Boolean {
        val supportAlipay =
            deviceStateLiveData?.value?.deviceCapability?.supportAliPay == true || BuildConfig.DEBUG
        Timber.i("isSupportAlipay $supportAlipay")
        return supportAlipay
    }

    fun removeRecordStateObserver() {
        Timber.i("removeRecordStateObserver")
        recordObserver?.removeRecordObserve()
    }
}
