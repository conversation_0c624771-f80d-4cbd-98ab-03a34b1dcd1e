@file:Suppress("TooG<PERSON>icExceptionCaught", "<PERSON><PERSON>anyFunctions", "Magic<PERSON><PERSON><PERSON>", "MaxLine<PERSON>eng<PERSON>")

package com.superhexa.supervision.feature.audioglasses.presentation.tools

import android.content.Context
import android.media.MediaPlayer
import android.os.Build
import android.view.WindowManager
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordPageViewModel.Companion.PACKAGE_SIZE
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordingFile
import com.superhexa.supervision.feature.audioglasses.presentation.recording.RecordingPhoneFile
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.player.ExoPlayerHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.NotifyHelper.curModel
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.response.ss.commoninfo.info.RecordingState
import com.superhexa.supervision.feature.channel.presentation.newversion.common.BleCons.Companion.BYTE_MASK
import com.superhexa.supervision.feature.channel.presentation.newversion.common.BleCons.Companion.BYTE_SHIFT
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.tools.IntentUtils
import com.superhexa.supervision.library.base.basecommon.tools.resumeCheckIsCompleted
import kotlinx.coroutines.suspendCancellableCoroutine
import timber.log.Timber
import java.io.BufferedInputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import kotlin.math.pow
import kotlin.math.sqrt

/**
 * 类描述:录音帮助类
 * 创建日期: 2024/9/2 15:03
 * 作者: qiushui
 */
object RecordingHelper {
    // 常量定义
    const val REC_TAG = "Recording_Tag"
    const val REC_CALL = 1
    const val REC_FACE_TO_FACE = 2
    const val REC_AUDIO_VIDEO = 3
    const val REC_GAIN = 8 // 波形增益
    const val REC_INDICATOR_HEIGHT = 280F // 蓝色指示线的高度
    const val LONG_100 = 100L
    const val LONG_500 = 500L
    const val LONG_800 = 800L
    const val LONG_1000 = 1000L
    const val SECONDS60 = 60
    const val HOURS3600 = 3600
    const val BIT_DEPTH8 = 8
    const val BIT_DEPTH16 = 16

    const val BYTE0_LENGTH = 1
    const val OFFSET_LENGTH = 4
    const val CRC32_LENGTH = 4

    const val REC_CHANNEL_1 = 1 // 通道数 1
    const val REC_CHANNEL_2 = 2 // 通道数 2
    const val REC_SAMPLE_RATE = 16000 // 采样率
    const val REC_FRAME_SIZE = (REC_SAMPLE_RATE / 1000 * REC_CHANNEL_1) * 20 // 帧大小
    const val CHUNK_SIZE: Int = 60 // 每次解码60字节

    /**
     * 从 ByteArray 中解析 Int
     */
    fun ByteArray.toInt(offset: Int, length: Int): Int {
        require(offset + length <= this.size) { "ByteArray length is insufficient to extract Int" }
        return ByteBuffer.wrap(this, offset, length).order(ByteOrder.BIG_ENDIAN).int
    }

    /**
     * 从 ByteArray 中解析 Int
     */
    fun ByteArray.toIntLittle(offset: Int, length: Int): Int {
        require(offset + length <= this.size) { "ByteArray length is insufficient to extract Int" }
        return ByteBuffer.wrap(this, offset, length).order(ByteOrder.LITTLE_ENDIAN).int
    }

    /**
     * 从 ByteArray 中解析数据内容
     */
    fun ByteArray.extractDataContent(startIndex: Int, endIndex: Int): ByteArray {
        require(startIndex in 0 until endIndex && endIndex <= this.size) { "Invalid range for data content extraction" }
        return this.copyOfRange(startIndex, endIndex)
    }

//    fun startRecordingService() {
//        val startIntent = Intent(LibBaseApplication.instance, RecordingService::class.java)
//        LibBaseApplication.instance.startService(startIntent)
//    }
//
//    fun stopRecordingService() {
//        val startIntent = Intent(LibBaseApplication.instance, RecordingService::class.java)
//        LibBaseApplication.instance.stopService(startIntent)
//    }
//
//    fun startDownloadService(context: Context = LibBaseApplication.instance) {
//        context.startService(Intent(context, DownloadService::class.java))
//    }
//
//    fun stopDownloadService(context: Context = LibBaseApplication.instance) {
//        context.stopService(Intent(context, DownloadService::class.java))
//    }

    fun getRecordingTypeString(recordingType: Int): String {
        return when (recordingType) {
            REC_CALL -> LibBaseApplication.instance.getString(R.string.ss2RecordCall)
            REC_FACE_TO_FACE -> LibBaseApplication.instance.getString(R.string.ss2RecordFaceToFace)
            REC_AUDIO_VIDEO -> LibBaseApplication.instance.getString(R.string.ss2RecordVideo)
            else -> ""
        }
    }

    fun safeTimestampForDay(fileName: String): String {
        return try {
            // 使用 "_" 分割文件名，提取时间戳和后缀
            val parts = fileName.split("_")
            // 处理没有后缀的情况
            if (parts.size == 1) {
                formatTimestampToDay(fileName.toLong())
            } else {
                // 处理有后缀的情况
                val timestamp = parts[0].toLongOrNull()
//                val suffix = parts.drop(1).joinToString("_") // 获取后缀部分
//                timestamp?.let { "${formatTimestampToDay(it)}_$suffix" } ?: fileName
                timestamp?.let { "${formatTimestampToDay(it)}（2）" } ?: fileName
            }
        } catch (e: Exception) {
            e.printStackTrace()
            fileName // 如果发生异常，返回文件名
        }
    }

    // app 自己获取的时间戳不需要乘以1000
    fun formatTimestampToDay(timestamp: Long): String {
        return try {
            val timestampStr = timestamp.toString()
            val mTimestamp = when {
                timestampStr.length < 13 -> {
                    val missingZeros = 13 - timestampStr.length
                    (timestamp * 10.0.pow(missingZeros.toDouble())).toLong()
                }

                else -> timestamp
            }
            val date = Date(mTimestamp)
            val format = SimpleDateFormat("M月d日 H时mm分", Locale.CHINA) // 定义格式
            format.format(date) // 返回格式化后的字符串
        } catch (e: Exception) {
            e.printStackTrace()
            "" // 如果发生异常，返回提示信息
        }
    }

    fun safeTimestampForDes(fileName: String): String {
        return try {
            // 使用 "_" 分割文件名，提取时间戳和后缀
            val parts = fileName.split("_")
            // 处理没有后缀的情况
            if (parts.size == 1) {
                formatTimestamp(fileName.toLong())
            } else {
                // 处理有后缀的情况
                val timestamp = parts[0].toLongOrNull()
                timestamp?.let { formatTimestamp(it) } ?: fileName
            }
        } catch (e: Exception) {
            e.printStackTrace()
            fileName // 如果发生异常，返回文件名
        }
    }

    fun formatTimestamp(timestamp: Long): String {
        val timestampStr = timestamp.toString()
        val mTimestamp = when {
            timestampStr.length < 13 -> {
                val missingZeros = 13 - timestampStr.length
                (timestamp * 10.0.pow(missingZeros.toDouble())).toLong()
            }

            else -> timestamp
        }
        val date = Date(mTimestamp)
        val format = SimpleDateFormat("MM/dd HH:mm", Locale.getDefault())
        return format.format(date)
    }

    fun formatSeconds(seconds: Int): String {
        val hours = seconds / HOURS3600
        val minutes = (seconds % HOURS3600) / SECONDS60
        val secs = seconds % SECONDS60

        return if (hours > 0) {
            // 格式化为 HH:mm:ss，传入明确的 Locale
            String.format(Locale.getDefault(), "%02d:%02d:%02d", hours, minutes, secs)
        } else {
            // 格式化为 mm:ss，传入明确的 Locale
            String.format(Locale.getDefault(), "%02d:%02d", minutes, secs)
        }
    }

    fun formatElapsedTime(elapsedTime: Double): String {
        // 转换成毫秒
        val milliseconds = (elapsedTime * 1000).toLong()

        // 计算总分钟、秒和毫秒
        val totalMinutes = milliseconds / 60000 // 总分钟数
        val seconds = (milliseconds / 1000) % 60 // 秒
        val millis = milliseconds % 1000 / 10 // 获取两位小数的毫秒

        // 格式化为 MM:ss.SS
        return String.format(Locale.getDefault(), "%02d:%02d.%02d", totalMinutes, seconds, millis)
    }

    fun formatElapsedTime2(milliseconds: Long): String {
        // 计算分钟、秒和毫秒
        val minutes = (milliseconds / 60000) % 60
        val seconds = (milliseconds / 1000) % 60
        val millis = milliseconds % 1000 / 10 // 获取两位小数的毫秒
        // 格式化为 MM:ss.SS
        return String.format(Locale.getDefault(), "%02d:%02d.%02d", minutes, seconds, millis)
    }

    fun createRecordingDirectory(context: Context = LibBaseApplication.instance): File? {
        // 获取外部文件目录
        val externalFilesDir = context.getExternalFilesDir(null)
        if (externalFilesDir != null) {
            // 在外部文件目录下创建 "recording" 子目录
            val recordingDir = File(externalFilesDir, "recording")

            // 检查目录是否存在
            if (!recordingDir.exists()) {
                val isCreated = recordingDir.mkdirs() // 如果不存在，创建目录
                if (isCreated) {
                    Timber.e("Recording 目录已创建: ${recordingDir.absolutePath}")
                } else {
                    Timber.e("Recording 目录创建失败")
                }
            } else {
                Timber.e("Recording 目录已存在: ${recordingDir.absolutePath}")
            }

            return recordingDir // 返回目录
        } else {
            Timber.e("外部文件目录不可用")
            return null // 如果外部文件目录不可用，返回null
        }
    }

    fun deleteMp3AndZipFiles(context: Context = LibBaseApplication.instance) {
        // 获取外部文件目录
        val externalFilesDir = context.getExternalFilesDir(null)
        if (externalFilesDir != null) {
            // 在外部文件目录下定位 "recording" 子目录
            val recordingDir = File(externalFilesDir, "recording")

            // 检查目录是否存在
            if (recordingDir.exists() && recordingDir.isDirectory) {
                // 列出目录下所有文件
                val files = recordingDir.listFiles { _, name ->
                    // 只选择 .zip 文件
                    name.endsWith(".zip")
                }

                // 删除所有符合条件的文件
                if (!files.isNullOrEmpty()) {
                    files.forEach { file ->
                        val isDeleted = file.delete()
                        if (isDeleted) {
                            Timber.e("文件已删除: ${file.absolutePath}")
                        } else {
                            Timber.e("文件删除失败: ${file.absolutePath}")
                        }
                    }
                } else {
                    Timber.e("没有 .mp3 或 .zip 文件需要删除")
                }
            } else {
                Timber.e("Recording 目录不存在")
            }
        } else {
            Timber.e("外部文件目录不可用")
        }
    }

    // 计算 PCM 文件的时长
    fun calculatePcmDuration(
        file: File,
        sampleRate: Int = REC_SAMPLE_RATE,
        channels: Int = REC_CHANNEL_1,
        bitDepth: Int = BIT_DEPTH16
    ): Double {
        val fileSizeInBytes = file.length() // 文件大小，单位为字节
        // 计算音频时长（秒）
        val durationInSeconds =
            fileSizeInBytes.toDouble() / (sampleRate * channels * (bitDepth / BIT_DEPTH8))
        return durationInSeconds
    }

    @Suppress("MagicNumber")
    suspend fun getMp3Duration(context: Context, file: File): Long = suspendCancellableCoroutine { conn ->
        conn.invokeOnCancellation {
            Timber.i("getMp3Duration invokeOnCancellation")
            conn.cancel()
        }
        Timber.e("getMp3Duration called path:${file.absolutePath}")
        val mediaPlayer = MediaPlayer()
        try {
            mediaPlayer.setDataSource(file.absolutePath)
            mediaPlayer.prepare()
            var duration = mediaPlayer.duration.toLong()
            Timber.e("getMp3Duration called duration:$duration")
            if (duration <= 0) {
                val exoPlayer = ExoPlayerHelper().apply {
                    init(context)
                }
                exoPlayer.setPlayListener(object : ExoPlayerHelper.PlayListener {
                    override fun onReady() {
                        super.onReady()
                        duration = exoPlayer.getDuration()
                        exoPlayer.release()
                    }
                })
            }
            conn.resumeCheckIsCompleted(duration, null)
        } catch (exception: Exception) {
            Timber.e("getMp3Duration mp3 duration exception:${exception.message}")
            conn.resumeCheckIsCompleted(0, null)
        }
    }

    fun getDirPath(): String {
        return LibBaseApplication.instance.getExternalFilesDir(null)
            .toString() + "/recording"
    }

    fun getDirFilePath(fileName: String): String {
        return LibBaseApplication.instance.getExternalFilesDir(null)
            .toString() + "/recording/$fileName"
    }

    fun getZipFilePath(): String {
        return LibBaseApplication.instance.getExternalFilesDir(null)
            .toString() + "/recording/recordings.zip"
    }

    fun getTempPcmFilePath(): String {
        return LibBaseApplication.instance.getExternalFilesDir(null)
            .toString() + "/recording/temp.pcm"
    }

    /**
     * 获取录音的PCM文件名称
     * 实时
     */
    fun getPCMPath(
        type: Int,
        context: Context = LibBaseApplication.instance
    ): Triple<Long, String, String> {
        val mType = when (type) {
            REC_CALL -> "CALL"
            REC_FACE_TO_FACE -> "FACE"
            else -> "AUDIO"
        }
        val path = context.getExternalFilesDir(null)?.path
        val currentTimeMillis = System.currentTimeMillis()
        val upPath = "$path/recording/REC_${currentTimeMillis}_UP_$mType.pcm"
        val dnPath = "$path/recording/REC_${currentTimeMillis}_DN_$mType.pcm"
        return Triple(currentTimeMillis, dnPath, upPath)
    }

    /**
     * 录音时直接转成Mp3
     */
    fun getMp3Path(
        type: Int,
        context: Context = LibBaseApplication.instance
    ): Triple<Long, String, String> {
        val mType = when (type) {
            REC_CALL -> "CALL"
            REC_FACE_TO_FACE -> "FACE"
            else -> "AUDIO"
        }
        val path = context.getExternalFilesDir(null)?.path
        val currentTimeMillis = System.currentTimeMillis()
        val upPath = "$path/recording/REC_${currentTimeMillis}_UP_$mType.mp3"
        val dnPath = "$path/recording/REC_${currentTimeMillis}_DN_$mType.mp3"
        return Triple(currentTimeMillis, dnPath, upPath)
    }

    /**
     * 获取录音的PCM文件名称
     * 文件推送
     */
    fun getPCMPathFromFile(
        fileName: String,
        type: Int,
        context: Context = LibBaseApplication.instance
    ): Triple<String, String, String> {
        val mType = when (type) {
            REC_CALL -> "CALL"
            REC_FACE_TO_FACE -> "FACE"
            else -> "AUDIO"
        }
        val path = context.getExternalFilesDir(null)?.path
        val upPath = "$path/recording/REC_${fileName}_UP_$mType.pcm"
        val dnPath = "$path/recording/REC_${fileName}_DN_$mType.pcm"
        return Triple(fileName, dnPath, upPath)
    }

    fun getMp3PathFromFile(
        fileName: String,
        type: Int,
        context: Context = LibBaseApplication.instance
    ): Triple<String, String, String> {
        val mType = when (type) {
            REC_CALL -> "CALL"
            REC_FACE_TO_FACE -> "FACE"
            else -> "AUDIO"
        }
        val path = context.getExternalFilesDir(null)?.path
        val upPath = "$path/recording/REC_${fileName}_UP_$mType.mp3"
        val dnPath = "$path/recording/REC_${fileName}_DN_$mType.mp3"
        return Triple(fileName, dnPath, upPath)
    }

    fun zipMultipleFiles(
        filesToZip: List<RecordingPhoneFile>,
        zipFilePath: String = getZipFilePath()
    ): String {
        return try {
            zipFiles(filesToZip, zipFilePath)
            File(zipFilePath).absolutePath
        } catch (e: IOException) {
            e.printStackTrace()
            ""
        }
    }

    private fun zipFiles(filesToZip: List<RecordingPhoneFile>, outZipPath: String) {
        try {
            FileOutputStream(outZipPath).use { fos ->
                ZipOutputStream(fos).use { zos ->
                    val uniqueFileNames = mutableSetOf<String>() // 用于跟踪已添加的文件名
                    for (filePath in filesToZip) {
                        val file = File(filePath.fileDnPath)
                        if (file.exists()) {
                            // 确保文件名唯一
                            var entryName = filePath.nickName.plus(".mp3")
                            var count = 2
                            while (!uniqueFileNames.add(entryName)) {
                                entryName = "${file.nameWithoutExtension} $count.${file.extension}"
                                count++
                            }

                            Timber.d("正在添加文件: %s (存储名: %s)", file.name, entryName)
                            FileInputStream(file).use { fis ->
                                val buffer = ByteArray(1024)
                                zos.putNextEntry(ZipEntry(entryName)) // 使用唯一名称
                                var length: Int
                                while (fis.read(buffer).also { length = it } > 0) {
                                    zos.write(buffer, 0, length)
                                }
                                zos.closeEntry()
                            }
                        } else {
                            Timber.e("文件不存在: %s", filePath) // 中文日志：文件不存在
                        }
                    }
                }
            }
            Timber.d("压缩文件已创建: %s", outZipPath) // 中文日志：压缩文件已创建
        } catch (ioe: IOException) {
            Timber.e(ioe, "压缩文件时出错") // 中文日志：压缩文件时出错
        }
    }

    // 分享单个文件
    fun shareSingleFile(context: Context, file: File) {
        IntentUtils.shareFile(
            context,
            file,
            IntentUtils.TYPE_ANY,
            context.getString(R.string.shareto)
        )
    }

    fun deleteFile(path: String, action: (() -> Unit)? = null) {
        action?.invoke()
        val file = File(path)
        if (file.exists() && file.isFile) {
            file.delete().also {
                if (it) {
                    Timber.tag(REC_TAG).e("文件已成功删除: ${file.name}")
                } else {
                    Timber.tag(REC_TAG).e("文件删除失败: ${file.name}")
                }
            }
        } else {
            Timber.tag(REC_TAG).e("文件不存在或不是一个文件: ${file.name}")
        }
    }

    /**
     * 修改文件名称的方法
     * @param file 原始文件
     * @param newFileName 新的文件名（不包含路径）
     * @return 修改后的文件或 null 如果修改失败
     */
    fun renameFile(file: File, newFileName: String, suffix: String = ".mp3"): File? {
        // 获取文件所在的目录路径
        val parentDir = file.parentFile ?: return null

        // 新的文件路径
        val newFile = File(parentDir, "${newFileName}$suffix")

        // 检查新的文件名是否已经存在
        if (newFile.exists()) {
            Timber.e("Error: A file with the new name already exists.")
            return newFile
        }

        // 尝试重命名文件
        return if (file.renameTo(newFile)) {
            Timber.d("File renamed successfully from ${file.name} to $newFileName")
            newFile
        } else {
            Timber.e("Error: Could not rename file.")
            null
        }
    }

    /**
     * 获取录音提示弹窗是否需要弹的KEY
     */
    fun recordingNoticeDialogKey(): String {
        return String.format(
            ConstsConfig.RecordingNoticeDialogWithModel,
            AccountManager.getUserID(),
            curModel
        )
    }

    /**
     * 分块读取 PCM 文件，每帧为 32 毫秒，采样率为 16000 Hz
     * @param file: PCM 文件
     * @param frameDurationMs: 每帧的时长，默认 32 毫秒
     * @param sampleRate: 采样率，默认 16000 Hz
     * @return List<ShortArray>: 每帧读取的 PCM 数据（ShortArray 列表）
     */
    fun readPcmFileInFrames(
        file: File,
        frameDurationMs: Float = 0.033333335f,
        sampleRate: Int = 16000
    ): List<ShortArray> {
        if (!file.exists()) {
            Timber.e("文件不存在: ${file.absolutePath}")
            return emptyList() // 如果文件不存在，则返回空列表或进行其他处理
        }
        // 计算每帧对应的采样点数
        val frameSize = (sampleRate * frameDurationMs).toInt() // 采样率 * 秒数 = 每帧的采样点数
        val bytePerFrame = frameSize * 2 // 每个采样点 2 个字节（16 位）

        val frameList = mutableListOf<ShortArray>()

        // 使用 BufferedInputStream 来分块读取数据
        BufferedInputStream(FileInputStream(file)).use { inputStream ->
            val buffer = ByteArray(bytePerFrame) // 缓冲区，每次读取一个帧的字节数
            var bytesRead: Int

            // 每次读取 bytePerFrame 大小的字节块，直到读完文件
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                // 如果读取到的字节数小于一帧的字节数，我们处理读取到的实际字节数
                val actualFrameSize = bytesRead / 2 // 读取到的字节转换为 Short 个数
                val shortArray = ShortArray(actualFrameSize)

                // 将字节转换为 ShortArray
                for (i in 0 until actualFrameSize) {
                    shortArray[i] =
                        ((buffer[i * 2 + 1].toInt() shl 8) or (buffer[i * 2].toInt() and 0xFF)).toShort()
                }
                frameList.add(shortArray) // 将读取到的帧数据添加到列表中
            }
        }

        return frameList
    }

    /**
     * 计算一帧的 RMS（均方根）值，代表音量
     * @param frame: 一帧的音频采样数据
     * @return Double: 该帧的 RMS 值
     */
    fun calculateRms(frame: ShortArray): Double {
        var sumOfSquares = 0.0
        for (sample in frame) {
            val normalizedSample = sample.toFloat() / Short.MAX_VALUE // 将样本归一化到 [-1, 1] 范围
            sumOfSquares += normalizedSample * normalizedSample
        }
        // 计算均方根（RMS）值
        val rms = sqrt(sumOfSquares / frame.size)

        // 将 RMS 值转化为音量大小，通常用于 UI 中显示音量
        return (rms * 100) // 返回音量百分比
    }

    /**
     * 从 PCM 数据块中获取每帧的音高数据 (RMS 值)
     * @param pcmChunks: PCM 数据的 ShortArray 列表（分块读取后的）
     * @return List<Double>: 每帧的 RMS 值列表
     */
    fun getAmplitudeDataFromChunks(pcmChunks: List<ShortArray>): List<Double> {
        val amplitudeList = mutableListOf<Double>()

        for (chunk in pcmChunks) {
            val rms = calculateRms(chunk) // 计算每块的 RMS 值
            amplitudeList.add(rms) // 将 RMS 值添加到列表中
        }
        return amplitudeList
    }

    /**
     * 从上下行的 PCM 数据块中获取每帧的最大音高数据
     * @param pcmChunksDn: 下行的 PCM 数据块
     * @param pcmChunksUp: 上行的 PCM 数据块
     * @return List<Double>: 每帧的最大 RMS 值列表
     */
    fun getMaxAmplitudeDataFromChunks(
        pcmChunksDn: List<ShortArray>,
        pcmChunksUp: List<ShortArray>
    ): List<Double> {
        val amplitudeList = mutableListOf<Double>()

        // 取上下行 PCM 数据的最小块数量，避免越界
        val minSize = minOf(pcmChunksDn.size, pcmChunksUp.size)

        // 比较上下行每一帧数据的 RMS 值，取最大值
        for (i in 0 until minSize) {
            val rmsDn = calculateRms(pcmChunksDn[i]) // 计算下行的 RMS 值并放大
            val rmsUp = calculateRms(pcmChunksUp[i]) // 计算上行的 RMS 值并放大

            // 取上下行中较大的 RMS 值
            val maxRms = maxOf(rmsDn, rmsUp)
            amplitudeList.add(maxRms) // 将较大的 RMS 值添加到列表中
        }

        return amplitudeList
    }

    /**
     * 检查文件是否存在，若存在则删除
     * @param filePath 文件路径
     * @return true 如果文件已成功删除或不存在，false 如果删除失败
     */
    fun checkAndDeleteFileIfExists(filePath: String): Boolean {
        val pcmFile = File(filePath)
        return if (pcmFile.exists()) {
            if (pcmFile.delete()) {
                Timber.tag(REC_TAG).i("文件已存在，已删除：$filePath")
                true
            } else {
                Timber.tag(REC_TAG).e("文件删除失败：$filePath")
                false
            }
        } else {
            true
        }
    }

    /**
     * 将一个16位整数转换为小端模式的字节数组
     * @param value 需要转换的16位整数
     * @return 长度为2的字节数组，按小端模式排列
     */
    fun intToLittleEndianBytes(value: Int = PACKAGE_SIZE): ByteArray {
        return byteArrayOf(
            (value and BYTE_MASK).toByte(), // 低字节 (LSB)
            ((value shr BYTE_SHIFT) and BYTE_MASK).toByte() // 高字节 (MSB)
        )
    }

    fun getRecordDes(state: RecordingState): String {
        val id = when (state) {
            is RecordingState.InCallRecording -> R.string.ss2RecordCall
            is RecordingState.AudioVideoRecording -> R.string.ss2RecordVideo
            is RecordingState.FaceToFaceRecording -> R.string.ss2RecordFaceToFace
            else -> R.string.libs_empty
        }
        return LibBaseApplication.instance.getString(id)
    }

    fun getScreenRefreshRate(context: Context): Float {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11 (API 30) 及以上版本
            context.display.refreshRate
        } else {
            // Android 11 以下版本
            windowManager.defaultDisplay.refreshRate
        }
    }

    fun sortByTimestampGlassesList(list: List<RecordingFile>): List<RecordingFile> {
        return list.sortedByDescending { file ->
            val parts = file.fileName.split("_")
            parts.firstOrNull()?.toLongOrNull() ?: 0L
        }
    }

    fun sortByTimestampPhoneList(list: List<RecordingPhoneFile>): List<RecordingPhoneFile> {
        return list.sortedByDescending { file ->
            val parts = file.fileName.split("_")
            val timestamp = parts.firstOrNull()?.toLongOrNull() ?: 0L
            if (timestamp < 1_000_000_000_000L) { // 小于13位，补足为13位
                timestamp * 10.0.pow(13 - timestamp.toString().length).toLong()
            } else {
                timestamp
            }
        }
    }

    fun ensureUniqueNickNames(recordingFiles: List<RecordingPhoneFile>): List<RecordingPhoneFile> {
        val nickNameMap = mutableMapOf<String, Int>() // 用于记录每个 nickName 的出现次数
        return recordingFiles.map { file ->
            var newNickName = file.nickName
            if (newNickName.isNotEmpty()) {
                val count = nickNameMap[newNickName] ?: 0
                if (count > 0) {
                    // 动态生成新的 nickName
                    newNickName = "$newNickName $count"
                }
                nickNameMap[file.nickName] = count + 1
            }
            // 返回新的对象，更新 nickName
            file.copy(nickName = newNickName)
        }
    }
}
