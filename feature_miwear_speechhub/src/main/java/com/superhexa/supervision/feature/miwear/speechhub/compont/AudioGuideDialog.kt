package com.superhexa.supervision.feature.miwear.speechhub.compont

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.superhexa.supervision.feature.miwear.speechhub.R
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheet
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF_30
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9_30
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_50
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_18

/**
 * 音频转录使用引导弹窗
 * 根据 Figma 设计图实现：https://www.figma.com/design/quvRQ9Bet6gj3nz2PA0t9U/小米眼镜-UI?node-id=18539-96413
 * 创建日期: 2025/7/31
 * 作者: shouzheng
 */

data class GuideItem(
    val imageRes: Int,
    val titleRes: Int
)

@Composable
fun AudioGuideDialog(
    visible: Boolean,
    onDismiss: () -> Unit
) {
    val guideItems = listOf(
        GuideItem(
            imageRes = R.mipmap.audio_new_guide_01,
            titleRes = R.string.audio_guide_title_01
        ),
        GuideItem(
            imageRes = R.drawable.audio_new_guide_02,
            titleRes = R.string.audio_guide_title_02
        ),
        GuideItem(
            imageRes = R.drawable.audio_new_guide_03,
            titleRes = R.string.audio_guide_title_03
        ),
        GuideItem(
            imageRes = R.drawable.audio_new_guide_04,
            titleRes = R.string.audio_guide_title_04
        )
    )

    BottomSheet(
        visible = visible,
        onDismiss = onDismiss
    ) {
        Card(
            shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp),
            backgroundColor = Color18191A,
            elevation = Dp_0,
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(max = 657.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 19.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 顶部间距 - 根据设计图调整
                Spacer(modifier = Modifier.height(27.dp))

                // 标题 - 根据设计图样式调整
                Text(
                    text = stringResource(R.string.audio_guide_dialog_title),
                    fontSize = Sp_18,
                    fontWeight = FontWeight.W600,
                    color = ColorWhite,
                    textAlign = TextAlign.Center
                )

                // 标题下方间距
                Spacer(modifier = Modifier.height(19.dp))

                // 引导内容列表
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    verticalArrangement = Arrangement.spacedBy(28.dp) // 根据设计图调整间距
                ) {
                    itemsIndexed(guideItems) { index, item ->
                        GuideItemView(
                            item = item,
                            isLast = index == guideItems.size - 1
                        )
                    }

                    // 添加免责声明文字到列表中，使其随列表滚动
                    item {
                        Text(
                            text = stringResource(R.string.audio_guide_disclaimer),
                            fontSize = Sp_13,
                            fontWeight = FontWeight.W400,
                            color = ColorWhite.copy(alpha = 0.6f),
                            textAlign = TextAlign.Start,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(start = 0.dp, end = 0.dp, top = 0.dp, bottom = 28.dp)
                        )
                    }
                }

                // 添加分割线
                Spacer(modifier = Modifier.height(1.dp).fillMaxWidth().background(ColorWhite.copy(alpha = 0.1f)))

                // 列表底部间距
                Spacer(modifier = Modifier.height(20.dp))

                // 底部按钮
                SubmitButton(
                    subTitle = stringResource(R.string.audio_guide_got_it),
                    enable = true,
                    textColor = ColorBlack,
                    enableColors = listOf(Color26EAD9, Color17CBFF),
                    disableColors = listOf(Color26EAD9_30, Color17CBFF_30),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(Dp_50)
                ) {
                    onDismiss()
                }

                // 底部间距
                Spacer(modifier = Modifier.height(28.dp))
            }
        }
    }
}

@Composable
private fun GuideItemView(
    item: GuideItem,
    isLast: Boolean
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.Start
    ) {
        // 描述文字
        Text(
            text = stringResource(item.titleRes),
            fontSize = Sp_16,
            fontWeight = FontWeight.W400,
            color = ColorWhite.copy(alpha = 0.8f),
            textAlign = TextAlign.Start,
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 0.dp, end = 0.dp)
        )

        // 文字和图片之间的间距
        Spacer(modifier = Modifier.height(12.dp))

        // 引导图片
        Image(
            painter = painterResource(id = item.imageRes),
            contentDescription = null,
            contentScale = ContentScale.FillWidth, // 改为填充宽度以匹配设计图
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .clip(RoundedCornerShape(16.dp))
        )
    }
}
