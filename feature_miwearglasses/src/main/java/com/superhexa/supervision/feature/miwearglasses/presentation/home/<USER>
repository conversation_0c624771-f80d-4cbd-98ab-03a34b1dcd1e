package com.superhexa.supervision.feature.miwearglasses.presentation.home

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Modifier
import androidx.core.os.bundleOf
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseBinderAdapter
import com.jeremyliao.liveeventbus.LiveEventBus
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.CameraJointDetectionManager
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.alert.MiWearAlertStatusHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.ota.MiWearDeviceOTAProgressHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.companion.DeviceCompanionManager
import com.superhexa.supervision.feature.channel.presentation.newversion.companion.event.DeviceAppearedEvent
import com.superhexa.supervision.feature.miwearglasses.R
import com.superhexa.supervision.feature.miwearglasses.databinding.FragmentMiwearHomeBinding
import com.superhexa.supervision.feature.miwearglasses.presentation.alert.AlertOtaDialogFragment
import com.superhexa.supervision.feature.miwearglasses.presentation.alert.AlertStateDialogFragment
import com.superhexa.supervision.feature.miwearglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.miwearglasses.presentation.space.dialogs.viewBinding
import com.superhexa.supervision.feature.miwearglasses.presentation.space.events.O95NetDisconnectEvent
import com.superhexa.supervision.feature.miwearglasses.presentation.space.events.O95TranCompleteEvent
import com.superhexa.supervision.feature.xiaoai.AiSpeechRepository
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.commonbean.MiWearUpgradeInfo
import com.superhexa.supervision.library.base.basecommon.compose.TitleBarHome
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.FRAGMENT_FIRM_TAG
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.safeActivity
import com.superhexa.supervision.library.base.basecommon.theme.Dp_32
import com.superhexa.supervision.library.base.basecommon.tools.ImmersiveManager
import com.superhexa.supervision.library.base.basecommon.tools.StatusBarUtil.setTransparent
import com.superhexa.supervision.library.base.presentation.dialog.DialogPriority
import com.superhexa.supervision.library.base.presentation.dialog.PriorityDialogManager
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.crash.upgrade.UpgradeManager
import com.superhexa.supervision.library.statistic.O95Statistic
import com.xiaomi.mis.manager.MisManager
import com.xiaomi.wear.protobuf.nano.SystemProtos
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:
 * 创建日期: 2025/5/31 on 12:10
 * 作者: qintaiyuan
 */
@Suppress("TooManyFunctions")
abstract class MiWearHomeBaseFragment : InjectionFragment(R.layout.fragment_miwear_home) {
    protected val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    protected val viewBinding by viewBinding<FragmentMiwearHomeBinding>()
    protected val viewModel by instance<MiWearHomeViewModel>()
    private val adapter by lazy { BaseBinderAdapter() }
    private val guideItemContent by lazy { MiWearHomeGuideContentBinder() }
    protected val showDeviceDot = mutableStateOf(false)
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        refreshMediaThumbData("onViewCreated")
        viewModel.sendEvent(MiWearHomeEvent.InitRecordObserver(viewLifecycleOwner))
        initComposeView()
        initRecyclerview()
        initListener()
        LiveEventBus.get(DeviceAppearedEvent::class.java).observe(this) { observer ->
            Timber.d("收到设备经典蓝牙连接Event:${observer.bondDevice}")
            if (bondDevice?.mac.equals(observer.bondDevice.mac, true)) {
                sendRefreshEvent()
            }
        }
        EventBus.getDefault().register(this)
    }

    private fun initComposeView() {
        viewBinding.cvTitleBar.setContent {
            val isImmersive = rememberIsImmersive()
            TopBar(
                modifier = Modifier
                    .then(
                        if (isImmersive) {
                            Modifier.statusBarsPadding()
                        } else {
                            Modifier
                        }
                    )
                    .heightIn(min = Dp_32)
                    .fillMaxWidth()

            )
        }
        viewBinding.cvHeaderView.setContent(contentView)
    }

    abstract val contentView: @Composable () -> Unit
    abstract fun onGuideItemClick(item: GuideItem)

    /**
     * 设置recycleView各种数据
     */
    private fun initRecyclerview() {
        adapter.addItemBinder(GuideItem::class.java, guideItemContent)
        viewBinding.recyclerView.layoutManager = LinearLayoutManager(context)
        viewBinding.recyclerView.setHasFixedSize(true)
        viewBinding.recyclerView.isNestedScrollingEnabled = false
        viewBinding.recyclerView.adapter = adapter
        adapter.setList(viewModel.mState.value.guideItems)
    }

    private fun initListener() {
        adapter.addChildClickViewIds(R.id.ivGuideIcon)
        adapter.setOnItemClickListener { adapter, _: View, position ->
            val item = adapter.data.get(position)
            if (item is GuideItem) {
                onGuideItemClick(item)
            }
        }
        viewBinding.swipeRefreshLayout.setOnRefreshListener {
            sendRefreshEvent(true)
            launch {
                delay(DELAY_TIME)
                viewBinding.swipeRefreshLayout.isRefreshing = false
            }
        }
    }

    @Composable
    protected fun rememberIsImmersive(): Boolean {
        return ImmersiveManager.isImmersive.value
    }

    @Composable
    private fun TopBar(modifier: Modifier) {
        val appDot = UpgradeManager.updateLiveData.observeAsState()
        TitleBarHome(
            modifier = modifier,
            showAccountDot = appDot.value ?: false,
            showDevicesDot = showDeviceDot.value,
            accountRes = R.drawable.home_mine_icon,
            devicesRes = R.drawable.home_more_icon,
            onAccountClick = {
                HexaRouter.Profile.navigateToPersion(this)
                O95Statistic.clickHomePageEvent("my_info")
            },
            onDevicesClick = {
                HexaRouter.Device.navigateToDeviceList(this)
                O95Statistic.clickHomePageEvent("device_manage")
            }
        )
    }

    /**
     * 显示升级弹窗
     */
    protected fun syncDeviceUpdateState(updateInfo: MiWearUpgradeInfo?) {
        Timber.d("syncDeviceUpdateState called updateInfo ==> $updateInfo")
        val isOTAState = MiWearDeviceOTAProgressHandler.isOtaState()
        Timber.d("syncDeviceUpdateState called isOTAState ==> $isOTAState")
        PriorityDialogManager.dismissCurrentDialogByTag(FRAGMENT_FIRM_TAG)
        if ((updateInfo != null || isOTAState) && !viewModel.isDisconnected()) {
            viewModel.sendEvent(
                MiWearHomeEvent.CheckCurrentOTAState {
                    if (updateInfo != null) {
                        PriorityDialogManager.showDialog(
                            ARouterTools.showDialogFragment(RouterKey.device_DeviceUpdateFragment)
                                .apply {
                                    arguments = bundleOf(
                                        BundleKey.DeviceRoomUpdateInfo to updateInfo.convert(
                                            bondDevice?.model?.toInt() ?: 0
                                        ),
                                        BundleKey.DeviceUpdatePageFrom to getPageName()
                                    )
                                },
                            childFragmentManager,
                            FRAGMENT_FIRM_TAG,
                            DialogPriority.MEDIUM
                        )
                    }
                }
            )
        }
        showDeviceDot.value = updateInfo != null
    }

    protected fun syncAlertState(alertStatus: SystemProtos.AlertStatus?) {
        // 以下弹框为电量、内存、机身温度、升级的提示，其他类型的alertStatus不需要以下弹框
        Timber.d("syncAlertState called alertStatus:$alertStatus")
        if (isNeedExit(alertStatus)) {
            return
        }
        val alertType = "MiWearAlertStatusDialog_${alertStatus?.let { getAlertType(it) }}"
        if (!PriorityDialogManager.checkIsInQueue(alertType) && !viewModel.isDisconnected()) {
            PriorityDialogManager.showDialog(
                if (MiWearAlertStatusHandler.checkHasOTAData(alertStatus)) {
                    getAlertOtaDialog(alertStatus)
                } else {
                    getAlertStatusDialog(alertStatus)
                },
                childFragmentManager,
                alertType,
                DialogPriority.MEDIUM
            )
        }
    }

    private fun getAlertStatusDialog(alertStatus: SystemProtos.AlertStatus?): AlertStateDialogFragment {
        return AlertStateDialogFragment().apply {
            setAlertStateInfo(alertStatus)
            addCallback(
                onDimiss = { viewModel.sendEvent(MiWearHomeEvent.ClearAlertStatusInfoEvent) },
                onAction = {
                    viewModel.sendEvent(MiWearHomeEvent.ClearAlertStatusInfoEvent)
                    HexaRouter.AudioGlasses.navigateToFileSpace(this)
                }
            )
        }
    }

    private fun getAlertOtaDialog(alertStatus: SystemProtos.AlertStatus?): AlertOtaDialogFragment {
        return AlertOtaDialogFragment().apply {
            setAlertStateInfo(alertStatus)
            addCallback(
                onAction = {
                    val otaState = alertStatus?.ota?.status ?: 1
                    viewModel.sendEvent(MiWearHomeEvent.ClearAlertStatusInfoEvent)
                    HexaRouter.AudioGlasses.navigateToOTA(this, otaState)
                }
            )
        }
    }

    private fun getAlertType(alertStatus: SystemProtos.AlertStatus): String {
        return if (MiWearAlertStatusHandler.checkHasOTAData(alertStatus)) {
            "ota"
        } else if (alertStatus.battery != null && alertStatus.battery.type == 1) {
            "low_battery"
        } else if (alertStatus.battery != null && alertStatus.battery.type == 2) {
            "saving_battery"
        } else if (alertStatus.temperature != null && alertStatus.temperature.high) {
            "temperature"
        } else if (alertStatus.storage != null) {
            "storage"
        } else {
            "alert"
        }
    }

    private fun refreshMediaThumbData(tag: String) {
        Timber.d("MiWearHomeFragment--refreshMediaThumbData-$tag")
        launch {
            delay(DELAY_TIME)
            viewModel.sendEvent(MiWearHomeEvent.SyncThumbData)
            MisManager.reConnectMis(LibBaseApplication.instance)
        }
    }

    private fun isNeedExit(alertStatus: SystemProtos.AlertStatus?): Boolean {
        return alertStatus == null || (
            alertStatus.battery == null && alertStatus.temperature == null &&
                alertStatus.storage == null && alertStatus.ota == null
            )
    }

    /**
     * 处理 `CameraJointDetectionManager` 的异步逻辑，防止 `Compose` UI 线程阻塞
     */
    protected fun navigateIfJointReady(action: () -> Unit) {
        CameraJointDetectionManager.checkIsChannelSuccess {
            CameraJointDetectionManager.checkIsJointState {
                action()
            }
        }
    }

    private fun sendRefreshEvent(resetNewBindState: Boolean = false) {
        Timber.d("sendRefreshEvent,resetNewBindState:$resetNewBindState")
        DeviceUtils.checkBlueToothAndLocation(this) {
            Timber.d("sendRefreshEvent,action:$it")
            when (it) {
                DeviceUtils.Allgranted -> {
                    viewModel.sendEvent(MiWearHomeEvent.ActivelyRefreshEvent(resetNewBindState))
                    DeviceUtils.requestBackLocation(this, it)
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: Any) {
        when (event) {
            is O95TranCompleteEvent,
            is O95NetDisconnectEvent -> {
                refreshMediaThumbData(event.javaClass.simpleName)
            }
        }
    }

    protected fun immersiveStatusBar() {
        safeActivity()?.apply {
            ImmersiveManager.setImmersive(true)
            setTransparent(this)
        }
    }

    // 小爱SDK init.
    private fun initMiLite() {
        AiSpeechRepository.startAiSpeech(requireContext())
        val isDeviceAssociated =
            DeviceCompanionManager.INSTANCE.isDeviceAssociated(requireActivity(), bondDevice?.mac)
        Timber.d("initMiLite device is associated:$isDeviceAssociated")
    }

    override fun onResume() {
        super.onResume()
        viewModel.sendEvent(MiWearHomeEvent.GetInitialRecordStatus)
        if (DeviceUtils.isDevicePermissionAllgranted()) {
            sendRefreshEvent()
        }
        initMiLite()
//        recoverStatusBar()
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    companion object {
        internal const val LINE_SPAN = 3
        private const val DELAY_TIME = 3_500L
        internal const val HOUR_OF_24 = 24 * 60 * 60 * 1000L
    }
}
