package com.superhexa.supervision.feature.miwearglasses.presentation.media.process

import android.os.IBinder
import android.os.RemoteException
import android.text.TextUtils
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.supervision.feature.miwearglasses.presentation.media.tool.Constans.ERROR_BINDER_DIED
import com.superhexa.supervision.feature.miwearglasses.presentation.media.tool.Constans.ERROR_PROCESS_EXCEPTION
import com.superhexa.supervision.feature.miwearglasses.presentation.media.tool.Constans.ERROR_PROCESS_NOT_STARTED
import com.superhexa.supervision.feature.miwearglasses.presentation.media.tool.Constans.ERROR_TIMEOUT
import com.superhexa.supervision.feature.miwearglasses.presentation.media.tool.Constans.JPEG_PROCESS_TIME_OUT
import com.superhexa.supervision.feature.miwearglasses.presentation.media.tool.Constans.SpaceTag
import com.superhexa.supervision.feature.miwearglasses.presentation.media.tool.Constans.VIDEO_PROCESS_BASE_TIME_OUT
import com.superhexa.supervision.feature.miwearglasses.presentation.media.tool.FileSpaceHelper
import com.superhexa.supervision.feature.miwearglasses.presentation.media.tool.FileSpaceHelper.fastCopyFile
import com.superhexa.supervision.feature.miwearglasses.presentation.media.tool.FileSpaceHelper.removeTargetFile
import com.superhexa.supervision.feature.miwearglasses.presentation.media.tool.FileSpaceHelper.renamePrivateVideoFile
import com.superhexa.supervision.feature.miwearglasses.presentation.space.events.O95DownloadProgressEvent
import com.superhexa.supervision.feature.miwearglasses.presentation.space.events.O95NetDisconnectEvent
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.TRANSFER_TYPE_OUT_OF_MEMORY
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.resumeCheckIsCompleted
import com.superhexa.supervision.library.base.tools.CoroutineBase
import com.superhexa.supervision.library.db.DbHelper
import com.superhexa.supervision.library.db.MediaType.Video_Folder
import com.superhexa.supervision.library.db.MediaType.Video_Type
import com.superhexa.supervision.library.db.MediaType.Yuv_Type
import com.superhexa.supervision.library.db.bean.MediaBean
import com.superhexa.supervision.library.statistic.O95Statistic
import com.xiaomi.algoprocessor.core.data.ProcessOutput
import com.xiaomi.algoprocessor.core.processor.BaseProcessor
import com.xiaomi.algoprocessor.core.processor.BaseProcessor.ProcessorListener
import com.xiaomi.algoprocessor.core.processor.JpegProcessor
import com.xiaomi.algoprocessor.core.processor.ProcessParams
import com.xiaomi.algoprocessor.core.processor.VideoProcessor
import com.xiaomi.algoprocessor.core.processor.server.BaseProcessorServer.ERROR_CODE_NO_SPACE_LEFT
import com.xiaomi.algoprocessor.core.processor.server.BaseProcessorServer.ERROR_CODE_UNKNOWN
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/**
 * 类描述:
 * 创建日期: 2025/4/23 on 14:39
 * 作者: qintaiyuan
 */
@Suppress("TooManyFunctions")
abstract class BaseProcessHelper : CoroutineBase() {
    abstract val processTag: String
    protected val mProcessListener: ProcessorListener by lazy { getProcessListener() }
    private val successCallback = AtomicReference<((Int) -> Unit)?>(null)
    private val failedCallback = AtomicReference<((Int) -> Unit)?>(null)
    private val deathRecipient: IBinder.DeathRecipient by lazy { createDeathRecipient() }
    private var startTime: Long = 0

    @Volatile
    protected var mProcessor: BaseProcessor? = null

    @Volatile
    private var processing = false // 标识是否正在处理

    private fun updateProcessing(isProcess: Boolean) {
        processing = isProcess
    }

    private var timeoutJob: Job? = null

    @Suppress("LongMethod")
    suspend fun processMedia(task: MediaBean): Boolean {
        return suspendCancellableCoroutine { con ->
            val hasResumed = AtomicBoolean(false)
            con.invokeOnCancellation {
                Timber.tag(SpaceTag).i("$processTag invokeOnCancellation,processing:$processing")
                if (!processing) {
                    onProcessTimeOut(task.fileName)
                    syncFailedCallbacks(ERROR_PROCESS_EXCEPTION)
                }
            }
            kotlin.runCatching {
                // 设置处理状态为 true，表示正在处理中
                updateProcessing(true)
                val sourceFolderPath = getSourceFolderPath(task)
                val outputPath = task.path
                if (!TextUtils.equals(task.path, task.vidoTempPath)) {
                    removeTargetFile(outputPath)
                }
                val builder = getProcessParams(sourceFolderPath, outputPath, task.fileName)
                successCallback.set { extraCode ->
                    if (hasResumed.compareAndSet(false, true)) {
                        Timber.tag(SpaceTag).d(
                            "successCallback task.path: ${task.path}, " +
                                "exists: ${File(task.path).exists()}"
                        )
                        cancelTimeout()
                        safeDeleteIfNeeded(sourceFolderPath)
                        if (extraCode != ERROR_CODE_UNKNOWN) {
                            renameMediaFile(task, listOf(extraCode))
                        }
                        // 处理完成后重置 processing 状态
                        updateProcessing(false)
                        con.resumeCheckIsCompleted(true, null)
                    }
                }

                failedCallback.set { errorCode ->
                    if (hasResumed.compareAndSet(false, true)) {
                        cancelTimeout()
                        if (errorCode == ERROR_CODE_NO_SPACE_LEFT) {
                            EventBus.getDefault().post(O95NetDisconnectEvent(task, TRANSFER_TYPE_OUT_OF_MEMORY))
                        }
                        // 处理完成后重置 processing 状态
                        updateProcessing(false)
                        handleProcessFailure(builder, task, sourceFolderPath, errorCode, con)
                    }
                }
                if (mProcessor == null) {
                    mProcessor = initProcessor()
                }
                startTimeout(task)
                reportProcessBeginStatistic()
                val processStartedCode = if (mProcessor is JpegProcessor) {
                    (mProcessor as? JpegProcessor)?.process(builder.build())
                        ?: ERROR_PROCESS_NOT_STARTED
                } else {
                    (mProcessor as? VideoProcessor)?.process(builder.build())
                        ?: ERROR_PROCESS_NOT_STARTED
                }
                Timber.tag(SpaceTag)
                    .d("$processTag processStartedCode: $processStartedCode,identifier=${task.identifier}")
                // 根据是否成功启动处理，触发回调
                if (processStartedCode != ERROR_CODE_UNKNOWN) {
                    syncFailedCallbacks(processStartedCode)
                }
            }.getOrElse {
                Timber.tag(SpaceTag).e("$processTag identifier=${task.identifier},error=$it")
                if (it is RemoteException) {
                    syncFailedCallbacks(ERROR_BINDER_DIED)
                } else {
                    syncFailedCallbacks(ERROR_PROCESS_EXCEPTION)
                }
            }
        }
    }

    private fun getProcessListener(): ProcessorListener {
        return object : ProcessorListener {
            override fun onProcessSequenceStarted(token: String) {
                Timber.tag(SpaceTag).d("$processTag, onProcessSequenceStarted -- token=$token")
            }

            override fun onProcessSequenceCompleted(token: String, extraCode: Int) {
                Timber.tag(SpaceTag).d("$processTag, onProcessSequenceCompleted -- token=$token, extraCode=$extraCode")
                syncSuccessCallbacks(extraCode)
            }

            override fun onProcessSequenceFailed(token: String, errorCode: Int) {
                Timber.tag(SpaceTag).d("$processTag, onProcessSequenceFailed -- token=$token,errorCode:$errorCode")
                syncFailedCallbacks(errorCode)
            }

            override fun onProcessStarted(token: String) {
                Timber.tag(SpaceTag).d("$processTag, onProcessStarted -- token=$token")
            }

            override fun onProcessFailed(token: String, errorCode: Int) {
                Timber.tag(SpaceTag).d("$processTag, onProcessFailed -- token=$token")
            }

            override fun onProcessCompleted(output: ProcessOutput?, token: String?) {
                Timber.tag(SpaceTag).d("$processTag, onProcessCompleted -- token=$token")
            }

            override fun onProcessSequenceCanceled(token: String) {
                Timber.tag(SpaceTag).d("$processTag, onProcessSequenceCanceled -- token=$token")
            }
        }
    }

    private fun getSourceFolderPath(task: MediaBean): String {
        val originFileName = FileSpaceHelper.getOriginFileName(task)
        val fileDir = when (task.mimeType) {
            Yuv_Type, Video_Type -> originFileName.removeSuffix(".zip")
            else -> originFileName
        }
        return fileDir
    }

    private fun createDeathRecipient(): IBinder.DeathRecipient {
        return IBinder.DeathRecipient {
            Timber.tag(SpaceTag).d("$processTag, DeathRecipient -- binderDied")
            resetProcessor()
            if (processing) {
                Timber.tag(SpaceTag).d("$processTag is already processing syncFailedCallbacks")
                syncFailedCallbacks(ERROR_BINDER_DIED)
            }
        }
    }

    @Suppress("MaxLineLength")
    protected fun copySourceFile(task: MediaBean, errorCodes: List<Int>): Pair<Boolean, String> {
        Timber.tag(SpaceTag).d("$processTag failed copy thumb file,identifier=${task.identifier}")
        val inputPath = if (isVideoType(task)) {
            task.vidoTempPath
        } else {
            task.thumbnaiLocalPath
        }
        if (isVideoType(task) && !TextUtils.equals(task.path, task.vidoTempPath)) {
            Timber.tag(SpaceTag).d("$processTag 清理一下 path:${task.path} 对应的文件,identifier=${task.identifier}")
            removeTargetFile(task.path)
        }
        val outputPath = FileSpaceHelper.addErrorCodesToFileName(
            if (isVideoType(task)) {
                task.vidoTempPath
            } else {
                task.path
            },
            errorCodes
        )
        Timber.tag(SpaceTag).d("$processTag outputPath:$outputPath,inputPath:$inputPath,identifier=${task.identifier}")

        val result = if (isVideoType(task)) {
            val renamePrivateVideoFile = renamePrivateVideoFile(inputPath, outputPath)
            if (!renamePrivateVideoFile) {
                fastCopyFile(File(inputPath), File(outputPath))
            } else {
                true
            }
        } else {
            fastCopyFile(File(inputPath), File(outputPath))
        }
        updateMediaPath(task, outputPath, result)
        return result to outputPath
    }

    @Suppress("MaxLineLength")
    private fun renameMediaFile(task: MediaBean, codes: List<Int>): Pair<Boolean, String> {
        val inputPath = task.path
        val outputPath = FileSpaceHelper.addErrorCodesToFileName(inputPath, codes)
        Timber.tag(SpaceTag).d("$processTag rename Media File outputPath:$outputPath,inputPath:$inputPath,identifier=${task.identifier}")

        val result = if (isVideoType(task)) {
            val renamePrivateVideoFile = renamePrivateVideoFile(inputPath, outputPath)
            if (!renamePrivateVideoFile) {
                fastCopyFile(File(inputPath), File(outputPath))
            } else {
                true
            }
        } else {
            fastCopyFile(File(inputPath), File(outputPath))
        }
        updateMediaPath(task, outputPath, result)
        return result to outputPath
    }

    private fun isVideoType(task: MediaBean): Boolean {
        return task.mimeType == Video_Type || task.mimeType == Video_Folder
    }

    protected fun safeDeleteIfNeeded(filePath: String, sourcePath: String = "") {
        if (!DeviceModelManager.isEnableDump()) {
            removeTargetFile(filePath, sourcePath)
        }
    }

    protected fun updateMediaPath(task: MediaBean, path: String, success: Boolean) {
        if (success) {
            Timber.tag(SpaceTag).d("$processTag updateMediaPath:$path, identifier=${task.identifier}")
            task.path = path
            if (isVideoType(task)) {
                task.vidoTempPath = path
                DbHelper.saveDownloadProgressO95(task)
                EventBus.getDefault().post(
                    O95DownloadProgressEvent(
                        task.downloadProgress,
                        task.downloadState,
                        task.identifier,
                        task.path,
                        task.thumbnaiLocalPath,
                        task.vidoTempPath,
                        task.processState,
                        task.contentUri,
                        task.hasRevised
                    )
                )
            }
        }
    }

    private fun resetProcessor() {
        // 在 binderDied 触发时重新创建新的 JpegProcessor
        mProcessor?.unlinkToDeath(deathRecipient)
        mProcessor = initProcessor()
        Timber.tag(SpaceTag).d("$processTag, mProcessor has been reset.")
    }

    private fun initProcessor(): BaseProcessor {
        return createProcessor().apply {
            if (DeviceModelManager.isEnableDump()) {
                setDump(FileSpaceHelper.getDumpDirectory())
            }
            enableLog(true)
            linkToDeath(deathRecipient)
        }
    }

    abstract fun createProcessor(): BaseProcessor
    abstract fun getProcessParams(inputPath: String, outputPath: String, token: String): ProcessParams.Builder
    abstract fun handleProcessFailure(
        builder: ProcessParams.Builder,
        task: MediaBean,
        fileDir: String,
        errorCode: Int,
        con: CancellableContinuation<Boolean>
    )

    protected fun syncSuccessCallbacks(extraCode: Int) {
        Timber.tag(SpaceTag).d("$processTag,syncSuccessCallbacks")
        successCallback.getAndSet(null)?.invoke(extraCode)
        reportProcessEndStatistic(true)
    }

    protected fun syncFailedCallbacks(errorCode: Int) {
        Timber.tag(SpaceTag).d("$processTag,syncFailedCallbacks,errorCode:$errorCode")
        toastAfterProcessError(errorCode)
        failedCallback.getAndSet(null)?.invoke(errorCode)
        reportProcessEndStatistic(false, errorCode.toString())
    }

    @Suppress("MaxLineLength")
    private fun startTimeout(task: MediaBean) {
        // 取消之前的超时任务
        cancelTimeout()
        val duration = when (task.mimeType) {
            Video_Type, Video_Folder -> task.duration * VIDEO_PROCESS_BASE_TIME_OUT
            else -> JPEG_PROCESS_TIME_OUT
        }
        Timber.tag(SpaceTag).d("$processTag startTimeout duration:$duration,identifier=${task.identifier}")
        // 启动超时协程
        timeoutJob = launch {
            delay(duration)
            Timber.tag(SpaceTag).w("$processTag onProcessTimeout duration:$duration,processing:$processing,identifier=${task.identifier}")
            if (processing) {
                // 超时后调用失败回调
                onProcessTimeOut(task.fileName)
                syncFailedCallbacks(ERROR_TIMEOUT)
            }
        }
    }

    open fun onProcessTimeOut(token: String) {
        Timber.tag(SpaceTag).d("$processTag onProcessTimeOut cancel process,identifier=$token")
        val cancel = mProcessor?.cancel(token)
        Timber.tag(SpaceTag).d("$processTag onProcessTimeOut cancel is success:$cancel,identifier=$token")
    }

    private fun cancelTimeout() {
        Timber.tag(SpaceTag).d("$processTag cancelTimeout")
        timeoutJob?.cancel() // 取消超时协程
    }

    private fun toastAfterProcessError(errorCode: Int) {
        val isEnable = DeviceModelManager.isEnableDump()
        if (isEnable) {
            LibBaseApplication.instance.toast("算法处理处理发生异常--$errorCode,请尽快抓取日志！！！")
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun reportProcessBeginStatistic() = try {
        startTime = System.currentTimeMillis()
        O95Statistic.mediaProcessBegin(mProcessor is VideoProcessor)
    } catch (e: Exception) {
        Timber.tag(SpaceTag).d("reportProcessStatistic error: ${e.printDetail()}")
    }

    @Suppress("TooGenericExceptionCaught")
    private fun reportProcessEndStatistic(success: Boolean, errorCode: String = "") = try {
        O95Statistic.mediaProcessEnd(success, errorCode, startTime)
    } catch (e: Exception) {
        Timber.tag(SpaceTag).d("reportProcessStatistic error: ${e.printDetail()}")
    }

    fun release() {
        cancelTimeout()
        mProcessor?.unlinkToDeath(deathRecipient)
        mProcessor = null
        successCallback.set(null)
        failedCallback.set(null)
    }
}
