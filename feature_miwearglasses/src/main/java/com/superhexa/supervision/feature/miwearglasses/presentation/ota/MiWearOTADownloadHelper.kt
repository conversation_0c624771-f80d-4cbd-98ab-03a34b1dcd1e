package com.superhexa.supervision.feature.miwearglasses.presentation.ota

import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.tools.FileAndDirUtils
import com.superhexa.supervision.library.net.retrofit.download.FileDownloader
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.resumeWithException

/**
 * <AUTHOR>
 * @date 2025/6/25 16:53.
 * description：下载文件
 */
object MiWearOTADownloadHelper : CoroutineScope {

    private const val OTA_LOG = "MiWearOTADownloadHelper"
    private const val MAX_PROGRESS = 100
    private val downloadRunningMap = mutableMapOf<String, Boolean>()

    private val baseViewModelJob = SupervisorJob()

    override val coroutineContext: CoroutineContext
        get() = baseViewModelJob + Dispatchers.IO

    private val cachePath =
        instance.getExternalFilesDir("")?.absolutePath + File.separator + "device"
    var curProgress = 0

    private var onDownloading: ((Int, String) -> Unit)? = null

    // 用于发射下载异常的流（replay=1 确保新订阅者能收到最近一次异常）
    private val _downloadException = MutableSharedFlow<Throwable>()
    val downloadException: SharedFlow<Throwable> = _downloadException.asSharedFlow()

    fun setDownloadingCallback(onDownloading: (Int, String) -> Unit) {
        this.onDownloading = onDownloading
    }

    fun removeDownloadingCallback() {
        onDownloading = null
    }

    fun startDownload(updateInfo: DeviceUpdateInfo?) {
        launch {
            try {
                downloadFile(updateInfo)
            } catch (e: Exception) {
                Timber.e("startDownload exception > ${e.message}")
                _downloadException.emit(e) // 发射异常到流
            }
        }
    }

    private suspend fun downloadFile(updateInfo: DeviceUpdateInfo?) = withContext(Dispatchers.IO) {
        val fileName = getUpdateFileName(updateInfo)
        val cacheFile = File(getUpdatePatchFilePath(updateInfo))
        // 先清空一下遗留的无用的固件包
        FileAndDirUtils.deleteDirWithoutTargeFile(File(cachePath), cacheFile)
        // 如果目录不存在，先创建
        if (cacheFile.parentFile?.exists() == false) {
            cacheFile.parentFile?.mkdirs()
        }

        return@withContext suspendCancellableCoroutine { con ->
            con.invokeOnCancellation {
                Timber.i("invokeOnCancellation")
                con.cancel()
            }
            kotlin.runCatching {
                // 如果存在先删除下
                val downloadUrl = if (updateInfo?.onlineUrl?.isNotBlank() == true) {
                    updateInfo.onlineUrl ?: (updateInfo.url ?: "")
                } else {
                    updateInfo?.url ?: ""
                }
                delFile(updateInfo)
                if (downloadUrl.isEmpty()) {
                    Timber.e("downloadFile url is Empty")
                    con.resumeWithException(IllegalStateException("-2"))
                    return@runCatching
                }
                downloadRunningMap[downloadUrl] = true
                FileDownloader.downloadOrResume(
                    downloadUrl,
                    cacheFile,
                    onProgress = { progress, _, _ ->
                        when (progress) {
                            MAX_PROGRESS -> {
                                val downloadFilePath = cachePath + File.separator + fileName
                                onDownloading?.invoke(MAX_PROGRESS, downloadFilePath)
                            }

                            else -> {
                                curProgress = progress
                                onDownloading?.invoke(progress, "")
                            }
                        }
                    },
                    onException = {
                        Timber.d("$OTA_LOG onException %s", it.toString())
                        con.resumeWithException(it)
                    }
                )
            }.getOrElse {
                Timber.d("$OTA_LOG otaDownloadError %s", it.toString())
                con.resumeWithException(it)
            }
        }
    }

    fun getUpdatePatchFilePath(deviceUpdateInfo: DeviceUpdateInfo?): String {
        if (deviceUpdateInfo == null) return ""
        return cachePath + File.separator + getUpdateFileName(deviceUpdateInfo)
    }

    private fun getUpdateFileName(info: DeviceUpdateInfo?): String {
        if (info == null) return ""
        return "device_${info.componentId}_${info.version}_${info.channel}.zip"
    }

    fun isDownloading(downloadUrl: String) = downloadRunningMap[downloadUrl] == true

    fun reset(invoke: String) {
        Timber.d("reset called invoke:$invoke")
        downloadRunningMap.clear()
        FileDownloader.cancelDownload()
    }

    fun isSameMd5(md5: String?, updateInfo: DeviceUpdateInfo?): Boolean {
        val cacheFile = File(getUpdatePatchFilePath(updateInfo))
        val fileMd5 = FileAndDirUtils.computeDigestValue(cacheFile, "md5")
        return md5?.isNotEmpty() ?: false && md5 == fileMd5
    }

    private fun delFile(updateInfo: DeviceUpdateInfo?) {
        val cacheFile = File(getUpdatePatchFilePath(updateInfo))
        if (cacheFile.exists()) {
            Timber.d("delFile called need del")
            cacheFile.delete()
        }
    }
}
