package com.superhexa.supervision.feature.profile.presentation.feedback

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.WindowManager
import androidx.activity.addCallback
import androidx.core.content.ContextCompat
import androidx.core.widget.doOnTextChanged
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaO95SeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaSSSeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaSVSeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cndModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnsModel
import com.superhexa.lib.channel.model.DeviceModelManager.ss2Model
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.model.DeviceModelManager.sssModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.state.DeviceCheckAction
import com.superhexa.supervision.feature.channel.presentation.state.DeviceStateCheckManager
import com.superhexa.supervision.feature.profile.R
import com.superhexa.supervision.feature.profile.databinding.FragmentQuestionFeedBackBinding
import com.superhexa.supervision.feature.profile.presentation.feedback.QuestionFeedbackViewModel.Companion.EMPTY_ITEM
import com.superhexa.supervision.feature.profile.presentation.feedback.QuestionFeedbackViewModel.Companion.MIN_DESC_LENGTH
import com.superhexa.supervision.feature.profile.presentation.feedback.dialog.FeedbackStateDialogFragment
import com.superhexa.supervision.feature.profile.presentation.feedback.photo.SelectedPhotoAdapter
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.QUESTION_FEEDBACK_DATA
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.QUESTION_OPTION_DATA
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.KEY_QUESTION_TYPE
import com.superhexa.supervision.library.base.basecommon.extension.clearKeepScreenOn
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.observeStateIgnoreChanged
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.InputUtil
import com.superhexa.supervision.library.base.basecommon.tools.IntentUtils
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.log.LogFileCompressor
import com.superhexa.supervision.library.base.mediapicker.VideoThumbCache
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.customer.WrapContentLinearLayoutManager
import com.superhexa.supervision.library.base.presentation.dialog.CommonBottomHintDialog
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.net.retrofit.utils.NetWorkUtil
import com.superhexa.supervision.library.statistic.constants.ScreenCons.ScreenName_SV1_FEEDBACK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import java.io.File

/**
 * 类描述:问题反馈页面
 * 创建日期: 2021/8/16
 * 作者: QinTaiyuan
 */
@Suppress("UNUSED_EXPRESSION")
@ExperimentalCoroutinesApi
@Route(path = RouterKey.profile_QuestionFeedbackFragment)
class QuestionFeedbackFragment : InjectionFragment(R.layout.fragment_question_feed_back) {
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private val viewBinding: FragmentQuestionFeedBackBinding by viewBinding()
    private val viewModel: QuestionFeedbackViewModel by instance()
    private val adapter: SelectedPhotoAdapter by lazy { getSelectedAdapter() }
    private var feedbackStateDialog: FeedbackStateDialogFragment? = null
    private var longClickCount = 0
    private var currentDeviceModel = ""
    private val handler: Handler by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        Handler(Looper.getMainLooper()) {
            viewBinding.tvCopyLogs.visibleOrgone(true)
            viewBinding.tvOneKeyShare.visibleOrgone(true)
            return@Handler false
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        requireActivity().window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        super.onViewCreated(view, savedInstanceState)
        initListeners()
        initViews()
        initData()
        updataDescCount(0)
        currentDeviceModel = arguments?.getString(QUESTION_FEEDBACK_DATA, ssModel).toString()
        val questionType = arguments?.getInt(QUESTION_OPTION_DATA) ?: 0
        dispatchAction(FeedbackAction.ChooseDefaultQuestionType(questionType))
    }

    private fun initListeners() {
        viewBinding.titlebar.setOnBackClickListener {
            InputUtil.hideKeyboard(viewBinding.recyclerView)
            if (!checkExit()) {
                navigator.pop()
            }
        }
        viewBinding.tvQuestion.clickDebounce(viewLifecycleOwner) {
            InputUtil.hideKeyboard(viewBinding.etQuestionDesc)
            dispatchAction(
                FeedbackAction.SwitchQuestion(
                    this@QuestionFeedbackFragment,
                    currentDeviceModel
                )
            )
        }
        viewBinding.tvSubmit.clickDebounce(viewLifecycleOwner) {
            if (viewBinding.checkBoxRoomLog.isChecked) {
                dealSubmitActionWithChooseLog()
            } else {
                submitAction()
            }
        }

        viewBinding.etQuestionDesc.doOnTextChanged { text, _, _, _ ->
            updataDescCount(text?.length ?: 0)
        }

        requireActivity().onBackPressedDispatcher
            .addCallback(viewLifecycleOwner) {
                if (!checkExit()) {
                    navigator.pop()
                }
            }
        viewBinding.titlebar.setOnClickListener {
            dealContainClick()
        }

        viewBinding.tvCopyLogs.clickDebounce(viewLifecycleOwner) {
            doLogic(false)
        }
        viewBinding.tvOneKeyShare.clickDebounce(viewLifecycleOwner) {
            doLogic(true)
        }
    }

    private fun dealSubmitActionWithChooseLog() {
        if (currentDeviceModel != bondDevice?.model) {
            toast(getString(R.string.feedBackDeviceNoConnect).format(getDeviceName()))
            return
        }
        when {
            isMijiaSSSeriesDevice(currentDeviceModel) -> {
                submitAction()
            }

            isMijiaO95SeriesDevice(currentDeviceModel) -> {
                if (viewBinding.checkBoxRoomLog.isChecked && !NetWorkUtil.isWifiEnabled(requireContext())) {
                    toast(getString(R.string.tip_request_enable_wifi))
                    return
                }
                submitAction()
            }

            isMijiaSVSeriesDevice(currentDeviceModel) -> DeviceStateCheckManager.checkDeviceState(
                this@QuestionFeedbackFragment,
                DeviceCheckAction.QuestionFeedback
            ) { submitAction() }
        }
    }

    private fun getDeviceName(): String {
        val deviceRes = when (currentDeviceModel) {
            ssModel -> R.string.ssDefaultName
            sssModel -> R.string.sssDefaultName
            ss2Model -> R.string.ss2DefaultName
            o95cnModel, o95cndModel, o95cnsModel -> R.string.o95DefaultName
            else -> R.string.svDefaultName
        }
        return getString(deviceRes)
    }

    private fun submitAction() {
        dispatchAction(
            FeedbackAction.CommitFeedback(
                fragment = this@QuestionFeedbackFragment,
                viewBinding.etQuestionDesc.text.toString(),
                viewBinding.edContact.text.toString(),
                currentDeviceModel,
                viewBinding.checkBoxRoomLog.isChecked
            )
        )
    }

    private fun doLogic(share: Boolean) = launch(Dispatchers.IO) {
        val logPath = requireContext().getExternalFilesDir("")?.path + "/androidlog.zip"
        LogFileCompressor().getZippedLogData(
            requireContext(),
            zipFilePath = logPath
        )
        val logFile = File(logPath)
        launch(Dispatchers.Main) {
            if (share) {
                if (logFile.exists()) {
                    IntentUtils.shareFile(
                        requireContext(),
                        logFile,
                        IntentUtils.TYPE_ANY,
                        getString(R.string.shareto)
                    )
                } else {
                    toast("文件不存在")
                }
            } else {
                if (logFile.exists()) {
                    toast("复制成功")
                } else {
                    toast("复制失败")
                }
            }
        }
    }

    private fun initViews() {
        viewBinding.recyclerView.layoutManager = WrapContentLinearLayoutManager(
            requireContext(),
            RecyclerView.HORIZONTAL,
            false
        )
        viewBinding.recyclerView.adapter = adapter
    }

    private fun dealContainClick() {
        longClickCount++
        when {
            longClickCount == CLICK_COUNT -> {
                handler.sendEmptyMessageDelayed(CLICK_EMPTY_WHAT, DelaySeconds)
            }

            longClickCount > CLICK_COUNT -> {
                handler.removeMessages(CLICK_EMPTY_WHAT)
                handler.removeCallbacksAndMessages(null)
                handler.postDelayed({ longClickCount = 0 }, CLICK_DELAY_TIME)
            }
        }
    }

    private fun getSelectedAdapter() = SelectedPhotoAdapter().apply {
        addChildClickViewIds(R.id.ivPhoto, R.id.ivDelete)
        setOnItemChildClickListener { _, view, position ->
            val item = getItem(position)
            when {
                view.id == R.id.ivPhoto && EMPTY_ITEM == item -> {
                    dispatchAction(
                        FeedbackAction.ChoosePhoto(requireActivity().supportFragmentManager)
                    )
                }

                view.id == R.id.ivDelete -> {
                    dispatchAction(FeedbackAction.RemovePhoto(item))
                }
            }
        }
    }

    private fun updataDescCount(descCount: Int) {
        viewBinding.tvDescCount.text =
            requireContext().getString(R.string.questiondescCount).format(descCount)
        viewBinding.tvDescCount.setTextColor(
            ContextCompat.getColor(
                requireContext(),
                if (descCount < MIN_DESC_LENGTH) R.color.red else R.color.white_20
            )
        )
    }

    private fun initData() {
        viewModel.feedbackLiveData.run {
            observeStateIgnoreChanged(viewLifecycleOwner, FeedbackStates::fetchStatus) {
                showFeedbackStateTip(it)
            }

            observeState(viewLifecycleOwner, FeedbackStates::photoList) {
                adapter.setList(it)
            }
            observeState(viewLifecycleOwner, FeedbackStates::questionType) {
                sycnLogCheckBoxState(it)
                viewBinding.tvQuestion.text =
                    getText(it?.questionDesc ?: R.string.feedBackSelectQuestion)
            }

            observeState(viewLifecycleOwner, FeedbackStates::miwearQuestionClassify) {
                sycnLogCheckBoxState(miWearQuestionClassify = it)
                if (it?.wideTagContent == null) {
                    viewBinding.tvQuestion.text = getText(R.string.feedBackSelectQuestion)
                } else {
                    val content = "${it.wideTagContent}-${it.tagShowContent}"
                    viewBinding.tvQuestion.text = content
                    viewBinding.tvQuestion.text = content
                    MMKVUtils.encode(KEY_QUESTION_TYPE, content)
                    if (content.equals(context?.getString(R.string.multiple_devices_wake_up))) {
                        viewBinding.tvRoomLog.text = context?.getString(R.string.room_log_tip)
                    } else {
                        viewBinding.tvRoomLog.text = context?.getString(R.string.roomLogTip)
                    }
                }
            }
        }

        viewModel.feedbackCallback.observe(viewLifecycleOwner) {
            when (it) {
                is FeedbackEvent.ShowToast -> toast(it.msg)
            }
        }
    }

    private fun showFeedbackStateTip(state: FeedbackFetchStatus) {
        if (state is FeedbackFetchStatus.FeedbackDefault) return
        if (feedbackStateDialog == null || feedbackStateDialog?.isDimissed() == true) {
            feedbackStateDialog = FeedbackStateDialogFragment.newInstance(state) {
                when (it) {
                    is FeedbackFetchStatus.FeedbackSuccess -> navigator.pop()
                    is FeedbackFetchStatus.FeedbackNoConnect,
                    FeedbackFetchStatus.FeedbackConnectFailed,
                    FeedbackFetchStatus.FeedbackGetLogFailed -> {
                        dispatchAction(
                            FeedbackAction.RetryConnectAndFeedback(
                                fragment = this@QuestionFeedbackFragment,
                                viewBinding.etQuestionDesc.text.toString(),
                                viewBinding.edContact.text.toString(),
                                currentDeviceModel
                            )
                        )
                    }

                    is FeedbackFetchStatus.FeedbackNetErrorFailed -> {
                        dispatchAction(
                            FeedbackAction.NetErrorFeedback(
                                viewBinding.etQuestionDesc.text.toString(),
                                viewBinding.edContact.text.toString(),
                                currentDeviceModel,
                                viewBinding.checkBoxRoomLog.isChecked
                            )
                        )
                    }

                    else -> {}
                }
            }
            feedbackStateDialog?.show(childFragmentManager, "feedbackStateDialog")
        }
        feedbackStateDialog?.syncFeedbackState(state)
    }

    private fun checkExit(): Boolean {
        if (viewBinding.etQuestionDesc.text?.isNotBlank() == true) {
            val dialog = CommonBottomHintDialog(
                sureAction = {
                    navigator.pop()
                }
            )
            dialog.setTitleDesc(
                getString(R.string.feedbackConfirm)
            )
            dialog.show(childFragmentManager, "AlertDialogLikeIOS")
            return true
        }
        return false
    }

    private fun sycnLogCheckBoxState(
        questionClassify: QuestionClassify? = null,
        miWearQuestionClassify: MiWearQuestionClassify? = null
    ) {
        if (questionClassify?.needDeviceLog == true || miWearQuestionClassify?.needDeviceLog == true) {
            viewBinding.tvRoomLog.visibleOrgone(true)
            viewBinding.checkBoxRoomLog.visibleOrgone(true)
        } else {
            viewBinding.tvRoomLog.visibleOrgone()
            viewBinding.checkBoxRoomLog.visibleOrgone()
        }
    }

    private fun dispatchAction(action: FeedbackAction) {
        viewModel.dispatchAction(action)
    }

    override fun onDestroyView() {
        adapter.setOnItemClickListener(null)
        clearKeepScreenOn()
        VideoThumbCache.clear()
        super.onDestroyView()
    }

    companion object {
        const val CLICK_COUNT = 6
        const val CLICK_DELAY_TIME = 5000L
        const val CLICK_EMPTY_WHAT = 0
        const val DelaySeconds: Long = 500
    }

    override fun getPageName() = ScreenName_SV1_FEEDBACK
}
