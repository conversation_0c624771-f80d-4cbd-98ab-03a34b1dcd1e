package com.superhexa.supervision.feature.profile.presentation.feedback

import android.app.Application
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.domain.repository.MiWearBindRepository
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaO95SeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaSSSeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaSVSeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.isSS2Device
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.presentation.TaskState
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.lib.channel.tools.ConnectUtil
import com.superhexa.lib.channel.tools.DeviceUtils
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.sv.SVConnectState
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.wifi.MiWearWiFiConfigHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.wifi.MiWearWiFiConfigHandler.Companion.WIFI_AP
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.wifi.MiWearWiFiP2PConfigHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.wifi.MiWearWiFiP2PConfigHandler.WIFI_P2P
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.factory.DeviceDecoratorFactory
import com.superhexa.supervision.feature.profile.R
import com.superhexa.supervision.feature.profile.data.repository.CollaborativeLogRepository
import com.superhexa.supervision.feature.profile.domain.repository.MiscRepository
import com.superhexa.supervision.feature.profile.presentation.feedback.handler.FeedbackHandler
import com.superhexa.supervision.feature.profile.presentation.feedback.handler.O95DeviceLogDownloadHelper
import com.superhexa.supervision.feature.profile.presentation.feedback.handler.SSDeviceLogDownloadHelper
import com.superhexa.supervision.feature.profile.presentation.feedback.handler.SVDeviceLogDownloadHelper
import com.superhexa.supervision.feature.profile.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.KEY_QUESTION_TYPE
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
import com.superhexa.supervision.library.base.basecommon.extension.asLiveData
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.postState
import com.superhexa.supervision.library.base.basecommon.extension.setState
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.basecommon.tools.JsonUtils
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.mediapicker.SelectMediaDialog
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModel
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import com.superhexa.supervision.library.fds.respository.FdsRepository
import com.superhexa.supervision.library.net.retrofit.utils.NetWorkUtil
import com.xiaomi.aivs.capability.AiCapabilityWrapper
import com.xiaomi.wear.protobuf.nano.SystemProtos.WiFiAP
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 类描述:
 * 创建日期: 2021/8/16
 * 作者: QinTaiyuan
 */
@Suppress("TooManyFunctions", "MagicNumber", "LargeClass", "MaxLineLength")
class QuestionFeedbackViewModel(
    private val fdsRepository: FdsRepository,
    private val miscRepository: MiscRepository,
    private val appEnvironment: AppEnvironment,
    private val accountManager: AccountManager,
    private val context: Application,
    private val miWearBindRepository: MiWearBindRepository
) : BaseViewModel() {
    private val feedbackHandler by lazy {
        FeedbackHandler(
            fdsRepository,
            miscRepository,
            appEnvironment,
            accountManager,
            context,
            miWearBindRepository
        )
    }
    private val svDeviceLogDownloadHelper by lazy { SVDeviceLogDownloadHelper() }
    private val o95DeviceLogDownloadHelper by lazy { O95DeviceLogDownloadHelper() }
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val decorator: IDeviceOperator<SSstateLiveData> by lazy {
        DecoratorUtil.getDecorator(bondDevice)
    }
    private val svDecorator get() = DeviceDecoratorFactory.getCurSVDeviceDecorator()
    val feedbackCallback: LifecycleCallback<(FeedbackEvent) -> Unit> = LifecycleCallback()
    private val _feedbackLiveData = MutableLiveData(FeedbackStates(photoList = listOf(EMPTY_ITEM)))
    val feedbackLiveData = _feedbackLiveData.asLiveData()

    private val logPathList by lazy { CopyOnWriteArrayList<String>() }
    private val aiCapability = AiCapabilityWrapper.INSTANCE
    private val collaborativeLogRepository = CollaborativeLogRepository()

    private val handler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                CONNECT_DEVICE_TIME_OUT -> {
                    Timber.d("time out called")
                    connectDeviceFailCallback.invoke()
                }
            }
        }
    }

    private val connectDeviceFailCallback: () -> Unit = {
        Timber.d("%s 重连设备失败", FEED_BACK_LOG)
        _feedbackLiveData.postState {
            copy(fetchStatus = FeedbackFetchStatus.FeedbackConnectFailed)
        }
    }

    init {
        svDecorator?.liveData?.asFlow()?.map { it.state.connectState }?.distinctUntilChanged()
            ?.onEach {
                if (it == SVConnectState.BleDisConnected &&
                    _feedbackLiveData.value?.fetchStatus is FeedbackFetchStatus.FeedbackGetingLog
                ) {
                    viewModelScope.launch {
                        _feedbackLiveData.postState {
                            copy(fetchStatus = FeedbackFetchStatus.FeedbackGetLogFailed)
                        }
                        svDeviceLogDownloadHelper.stopDownloadRomLog()
                    }
                }
            }?.launchIn(viewModelScope)
    }

    fun dispatchAction(action: FeedbackAction) {
        when (action) {
            is FeedbackAction.SwitchQuestion -> switchQuestion(action)
            is FeedbackAction.ChoosePhoto -> choosePhoto(action)
            is FeedbackAction.RemovePhoto -> removePhoto(action.photoUrl)
            is FeedbackAction.CommitFeedback -> commitFeedback(action)
            is FeedbackAction.RetryConnectAndFeedback -> retryConnectAndFeedback(action)
            is FeedbackAction.NetErrorFeedback -> prepareFeedBack(
                action.questionDesc,
                action.contact,
                action.productId,
                action.selectedRoomLog
            )

            is FeedbackAction.ChooseDefaultQuestionType -> chooseDefultQuestionType(
                action.questionType
            )
        }
    }

    private fun chooseDefultQuestionType(questionType: Int) = viewModelScope.launch {
        val qustion = when (questionType) {
            QuestionClassify.ItemDeviceUpdate.questionType -> QuestionClassify.ItemDeviceUpdate
            QuestionClassify.ItemSSDeviceUpdate.questionType -> QuestionClassify.ItemSSDeviceUpdate
            else -> null
        }
        _feedbackLiveData.postState { copy(questionType = qustion) }
    }

    @Suppress("MaxLineLength")
    private fun switchQuestion(action: FeedbackAction.SwitchQuestion) {
        if (feedbackHandler.isMiWearFeedbackModel(action.deviceModel)) {
            HexaRouter.Profile.navigateToMiWearQuestionClassify(
                action.fragment,
                deviceModel = action.deviceModel,
                wideTagId = _feedbackLiveData.value?.miwearQuestionClassify?.wideTagId ?: 0,
                tagId = _feedbackLiveData.value?.miwearQuestionClassify?.tagId ?: 0
            ) {
                _feedbackLiveData.postState { copy(miwearQuestionClassify = it) }
            }
        } else {
            HexaRouter.Profile.navigateToQuestionClassify(
                action.fragment,
                _feedbackLiveData.value?.questionType?.questionType ?: 0,
                action.deviceModel
            ) {
                _feedbackLiveData.postState { copy(questionType = it) }
            }
        }
    }

    private fun choosePhoto(action: FeedbackAction.ChoosePhoto) {
        val selectMediaDialog = SelectMediaDialog()
        selectMediaDialog.listener = object : SelectMediaDialog.OnSelectedListener {
            override fun onSelect(list: MutableList<String>) {
                if (list.size != MAST_PHOTO) {
                    list.add(EMPTY_ITEM)
                }
                _feedbackLiveData.postState {
                    copy(photoList = list)
                }
            }
        }
        val arrayList = ArrayList(_feedbackLiveData.value?.photoList)
        arrayList.remove(EMPTY_ITEM)
        selectMediaDialog.syncSelectedData(arrayList)
        selectMediaDialog.arguments = Bundle().apply {
            putInt(BundleKey.MediaType, action.mediaType)
            putInt(BundleKey.MaxSelectNum, action.photoCount)
            putBoolean(BundleKey.IsShowCamera, action.isShowCamera)
        }
        selectMediaDialog.show(action.manager, "SelectMediaDialog")
    }

    private fun removePhoto(photoUrl: String) {
        _feedbackLiveData.value?.photoList?.let { oldlist ->
            val newList = ArrayList(oldlist)
            newList.remove(photoUrl)
            if (!newList.contains(EMPTY_ITEM)) {
                newList.add(EMPTY_ITEM)
            }
            _feedbackLiveData.setState {
                copy(photoList = newList)
            }
        }
    }

    private fun commitFeedback(action: FeedbackAction.CommitFeedback) {
        val questionType = _feedbackLiveData.value?.questionType ?: 0
        val tagId = _feedbackLiveData.value?.miwearQuestionClassify?.tagId ?: -1
        when {
            (feedbackHandler.isMiWearFeedbackModel(action.productId) && tagId == -1) ->
                dispatchEvent(FeedbackEvent.ShowToast(context.getString(R.string.questionTypeTip)))

            !feedbackHandler.isMiWearFeedbackModel(action.productId) && questionType == 0 ->
                dispatchEvent(FeedbackEvent.ShowToast(context.getString(R.string.questionTypeTip)))

            action.questionDesc.isBlank() || action.questionDesc.length < MIN_DESC_LENGTH ->
                dispatchEvent(
                    FeedbackEvent.ShowToast(context.getString(R.string.feedbackTooFewWords))
                )

            action.selectedRoomLog && getLastStoreBondDevice() != null -> dealRoomLogAction(action)
            else -> prepareFeedBack(action.questionDesc, action.contact, action.productId, false)
        }
    }

    private fun retryConnectAndFeedback(action: FeedbackAction.RetryConnectAndFeedback) =
        viewModelScope.launch {
            DeviceUtils.checkBlueToothAndLocation(action.fragment) {
                when (it) {
                    DeviceUtils.Allgranted -> {
                        reConnectDevice(action.productId, action.fragment) {
                            when {
                                isMijiaSSSeriesDevice(action.productId) -> {
                                    downloadSSRoomLog {
                                        prepareFeedBack(
                                            action.questionDesc,
                                            action.contact,
                                            action.productId,
                                            true
                                        )
                                    }
                                }

                                isMijiaO95SeriesDevice(action.productId) -> downloadO95RoomLog(action.fragment) {
                                    prepareFeedBack(action.questionDesc, action.contact, action.productId, true)
                                }

                                isMijiaSVSeriesDevice(action.productId) -> prepareFeedBack(
                                    action.questionDesc,
                                    action.contact,
                                    action.productId,
                                    true
                                )
                            }
                        }
                    }
                }
            }
        }

    private fun getLastStoreBondDevice(): BondDevice? {
        return BlueDeviceDbHelper.getBondDevice()
    }

    private fun isConnected(productId: String): Boolean {
        if ((bondDevice?.model ?: "") != productId) return false
        return when {
            isMijiaSSSeriesDevice(productId) ||
                isMijiaO95SeriesDevice(productId) -> decorator.isChannelSuccess()

            else -> svDecorator?.isChannelSuccess() == true
        }
    }

    private fun dealRoomLogAction(action: FeedbackAction.CommitFeedback) = viewModelScope.launch {
        Timber.d("dealRoomLogAction $action")
        when {
            !isConnected(action.productId) -> _feedbackLiveData.setState {
                copy(fetchStatus = FeedbackFetchStatus.FeedbackNoConnect)
            }

            isMijiaSSSeriesDevice(action.productId) -> downloadSSRoomLog {
                prepareFeedBack(action.questionDesc, action.contact, action.productId, true)
            }

            isMijiaO95SeriesDevice(action.productId) -> downloadO95RoomLog(action.fragment) {
                prepareFeedBack(action.questionDesc, action.contact, action.productId, true)

                // 调用接口一键上传眼镜协同唤醒日志
                val content = MMKVUtils.decodeString(KEY_QUESTION_TYPE)
                if (content.equals(context?.getString(R.string.multiple_devices_wake_up))) {
                    uploadCollaborativeLog(action.questionDesc)
                }
            }

            else -> reConnectDevice(action.productId, action.fragment) {
                prepareFeedBack(action.questionDesc, action.contact, action.productId, true)
            }
        }
    }

    /**
     * 眼镜协同唤醒一键日志上传
     */
    private fun uploadCollaborativeLog(questionDesc: String) {
        Timber.d("uploadCollaborativeLog")
        aiCapability.getToken {
            CoroutineScope(Dispatchers.IO).launch {
                var result = collaborativeLogRepository.uploadCollaborativeLog(
                    collaborativeContent = questionDesc,
                    token = it
                )
                if (result.isSuccess() && result.data?.result != null) {
                    Timber.d("uploadCollaborativeLog Success 播放反馈成功TTS")
                } else {
                    Timber.d("uploadCollaborativeLog fail 播放反馈失败TTS, result: $result")
                }
            }
        }
    }

    @Suppress("LongMethod")
    private fun reConnectDevice(currentModel: String, fragment: Fragment, action: () -> Unit) {
        when {
            isMijiaSSSeriesDevice(currentModel) ||
                isMijiaO95SeriesDevice(currentModel) -> {
                bondDevice?.let {
                    _feedbackLiveData.postState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbackConnecting)
                    }
                    Timber.d("%s 开始重连设备 currentModel=$currentModel", FEED_BACK_LOG)
                    if (isSS2Device(currentModel)) {
                        launchTimeout()
                    }
                    decorator.reConnect(
                        it,
                        onSuccess = {
                            handler.removeMessages(CONNECT_DEVICE_TIME_OUT)
                            Timber.d("%s 重连设备成功 currentModel=$currentModel", FEED_BACK_LOG)
                            action.invoke()
                        },
                        onFailed = connectDeviceFailCallback
                    )
                }
            }

            else -> {
                DeviceUtils.orderDeviceOpenWifi(fragment) {
                    when (it) {
                        TaskState.Loading -> {
                            Timber.d("%s 开始连接Wi-Fi", FEED_BACK_LOG)
                            _feedbackLiveData.postState {
                                copy(fetchStatus = FeedbackFetchStatus.FeedbackConnecting)
                            }
                        }

                        TaskState.StartCreateWifi -> {
                            Timber.d("%s 连接成功-开始打开热点", FEED_BACK_LOG)
                            _feedbackLiveData.postState {
                                copy(fetchStatus = FeedbackFetchStatus.FeedbacktCreateWifi)
                            }
                        }

                        TaskState.Success -> {
                            Timber.d("%s 连接Wi-Fi成功", FEED_BACK_LOG)
                            getSVRoomLogDownloadPath(action)
                        }
                        is TaskState.O95LowBattery -> {
                            Timber.d("%s 连接Wi-Fi失败 O95LowBattery", FEED_BACK_LOG)
                            _feedbackLiveData.postState {
                                copy(fetchStatus = FeedbackFetchStatus.FeedbackLowBattery)
                            }
                        }

                        is TaskState.O95Recording -> {
                            Timber.d("%s 连接Wi-Fi失败 O95Recording", FEED_BACK_LOG)
                            _feedbackLiveData.postState {
                                copy(fetchStatus = FeedbackFetchStatus.FeedbackDeviceRecording)
                            }
                        }

                        is TaskState.O95HighTemperature -> {
                            Timber.d("%s 连接Wi-Fi失败 O95HighTemperature", FEED_BACK_LOG)
                            _feedbackLiveData.postState {
                                copy(fetchStatus = FeedbackFetchStatus.FeedbackHighTemperature)
                            }
                        }
                        is TaskState.FailedPreStartWifi, is TaskState.FailedAfterStartWifi,
                        is TaskState.UserCancelOrConnectFailed -> {
                            Timber.d("%s 连接Wi-Fi失败", FEED_BACK_LOG)
                            _feedbackLiveData.postState {
                                copy(fetchStatus = FeedbackFetchStatus.FeedbackNoConnect)
                            }
                        }
                    }
                }
            }
        }
    }

    private fun launchTimeout() {
        Timber.d("launchTimeout called")
        handler.removeMessages(CONNECT_DEVICE_TIME_OUT)
        handler.sendEmptyMessageDelayed(CONNECT_DEVICE_TIME_OUT, TIME_OUT_DELAY)
    }

    private fun checkAppNetState(isSelectLog: Boolean, action: (Boolean) -> Unit) {
        val preNetWorkAvaiable = NetWorkUtil.isNetWorkAvaiable(context)
        launch(Dispatchers.IO) {
            when {
                preNetWorkAvaiable -> action.invoke(true)
                else -> action.invoke(
                    NetWorkUtil.checkUrlAvailable(
                        CHECK_AVALIABLE_URL,
                        totalRetryCount = if (isSelectLog) DEVICE_RETRY_COUNT else APP_RETRY_COUNT
                    )
                )
            }
        }
    }

    private fun getO95RoomLogDownloadPath(action: () -> Unit) = viewModelScope.launch {
        logPathList.clear()
        o95DeviceLogDownloadHelper.getO95RoomLogDownloadPath(
            onSuccess = { path ->
                logPathList.add(path)
                action.invoke()
            },
            onStart = {
                _feedbackLiveData.setState {
                    copy(fetchStatus = FeedbackFetchStatus.FeedbackGetingLog(3))
                }
            },
            onFailed = {
                _feedbackLiveData.postState {
                    copy(fetchStatus = FeedbackFetchStatus.FeedbackGetLogFailed)
                }
                launch {
                    closeO95Wifi()
                }
            }
        )
    }

    private suspend fun closeO95Wifi() {
        val ipAddress = MMKVUtils.decodeString(WIFI_P2P)
        if (DeviceModelManager.isEnableWiFiP2P() && ipAddress.isNotNullOrEmpty()) {
            MiWearWiFiP2PConfigHandler.removeGroupIfNeed()
        } else {
            MiWearWiFiConfigHandler().bindDecorator(decorator as IDeviceOperator<O95StateLiveData>).tryDisconnectWifiAp()
        }
    }

    private fun getSVRoomLogDownloadPath(action: () -> Unit) = viewModelScope.launch {
        logPathList.clear()
        svDeviceLogDownloadHelper.getSVRoomLogDownloadPath(
            onSuccess = { path ->
                logPathList.add(path)
                action.invoke()
            },
            onStart = {
                _feedbackLiveData.setState {
                    copy(fetchStatus = FeedbackFetchStatus.FeedbackGetingLog(3))
                }
            },
            onFailed = {
                _feedbackLiveData.postState {
                    copy(fetchStatus = FeedbackFetchStatus.FeedbackGetLogFailed)
                }
            }
        )
    }

    private fun downloadO95RoomLog(fragment: Fragment, action: () -> Unit) = viewModelScope.launch {
        if (DeviceModelManager.isEnableWiFiP2P()) {
            openMiWearWifiP2P(fragment, action)
        } else {
            openMiWearWifiAP(fragment, action)
        }
    }

    private fun openMiWearWifiP2P(fragment: Fragment, action: () -> Unit) {
        DeviceUtils.orderMiWearOpenWifiP2P(fragment) {
            when (it) {
                is TaskState.Loading -> {
                    Timber.d("%s 开始连接Wi-Fi P2P", FEED_BACK_LOG)
                    _feedbackLiveData.postState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbackConnecting)
                    }
                }

                is TaskState.StartCreateWifi -> {
                    Timber.d("%s 连接成功-开始打开热点", FEED_BACK_LOG)
                    _feedbackLiveData.postState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbacktCreateWifi)
                    }
                }

                is TaskState.O95LowBattery -> {
                    Timber.d("%s 连接Wi-Fi P2P O95LowBattery", FEED_BACK_LOG)
                    _feedbackLiveData.postState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbackLowBattery)
                    }
                }

                is TaskState.O95Recording -> {
                    Timber.d("%s 连接Wi-Fi P2P O95Recording", FEED_BACK_LOG)
                    _feedbackLiveData.postState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbackDeviceRecording)
                    }
                }

                is TaskState.O95HighTemperature -> {
                    Timber.d("%s 连接Wi-Fi P2P O95HighTemperature", FEED_BACK_LOG)
                    _feedbackLiveData.postState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbackHighTemperature)
                    }
                }

                is TaskState.FailedPreStartWifi,
                is TaskState.FailedAfterStartWifi,
                is TaskState.UserCancelOrConnectFailed -> {
                    Timber.d("%s 连接Wi-Fi P2P 失败", FEED_BACK_LOG)
                    _feedbackLiveData.postState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbackGetLogFailed)
                    }
                }

                is TaskState.Success -> {
                    Timber.d("%s 连接Wi-Fi P2P 成功", FEED_BACK_LOG)
                    getO95RoomLogDownloadPath(action)
                }
            }
        }
    }

    private fun openMiWearWifiAP(fragment: Fragment, action: () -> Unit) {
        DeviceUtils.orderMiWearOpenWifi(fragment) {
            when (it) {
                is TaskState.Loading -> {
                    Timber.d("%s 开始连接Wi-Fi", FEED_BACK_LOG)
                    _feedbackLiveData.postState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbackConnecting)
                    }
                }

                is TaskState.StartCreateWifi -> {
                    Timber.d("%s 连接成功-开始打开热点", FEED_BACK_LOG)
                    _feedbackLiveData.postState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbacktCreateWifi)
                    }
                }

                is TaskState.O95LowBattery -> {
                    Timber.d("%s 连接Wi-Fi失败 O95LowBattery", FEED_BACK_LOG)
                    _feedbackLiveData.postState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbackLowBattery)
                    }
                }

                is TaskState.O95Recording -> {
                    Timber.d("%s 连接Wi-Fi失败 O95Recording", FEED_BACK_LOG)
                    _feedbackLiveData.postState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbackDeviceRecording)
                    }
                }

                is TaskState.O95HighTemperature -> {
                    Timber.d("%s 连接Wi-Fi失败 O95HighTemperature", FEED_BACK_LOG)
                    _feedbackLiveData.postState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbackHighTemperature)
                    }
                }

                is TaskState.FailedPreStartWifi,
                is TaskState.FailedAfterStartWifi,
                is TaskState.UserCancelOrConnectFailed -> {
                    Timber.d("%s 连接Wi-Fi失败", FEED_BACK_LOG)
                    _feedbackLiveData.postState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbackGetLogFailed)
                    }
                }

                is TaskState.Success -> {
                    Timber.d("%s 连接Wi-Fi成功", FEED_BACK_LOG)
                    getO95RoomLogDownloadPath(action)
                }
            }
        }
    }

    private fun downloadSSRoomLog(action: () -> Unit) = viewModelScope.launch {
        logPathList.clear()
        val model = BlueDeviceDbHelper.getBondDevice()?.model ?: ssModel
        _feedbackLiveData.postState { // 优化需求统一展示需要几分钟 这里的值不再显示
            copy(fetchStatus = FeedbackFetchStatus.FeedbackGetingLog(0))
        }
        kotlin.runCatching {
            val logs = SSDeviceLogDownloadHelper.downloadDeviceLog(decorator, model)
            logPathList.add(logs.first)
            logPathList.add(logs.second)
            action.invoke()
        }.getOrElse {
            Timber.d("%s getRoomLogFailed---> %s", FEED_BACK_LOG, it.toString())
            _feedbackLiveData.postState {
                copy(fetchStatus = FeedbackFetchStatus.FeedbackGetLogFailed)
            }
        }
    }

    @Suppress("MaxLineLength")
    private suspend fun disConnectApWifiState(
        productId: String,
        retryCount: Int
    ): Boolean {
        if (isMijiaO95SeriesDevice(productId)) {
            closeO95Wifi()
        }
        ConnectUtil.disConnectApWifi(context)
        delay(DELAY)
        val ssid = if (isMijiaO95SeriesDevice(productId)) {
            val wifiAPJson = MMKVUtils.decodeString(WIFI_AP)
            val wiFiAP = JsonUtils.fromJson<WiFiAP>(wifiAPJson ?: "")
            wiFiAP?.ssid
        } else {
            BlueDeviceDbHelper.getWifiData()?.ssid
        }
        if ((
            ConnectUtil.isConnectedTo(context, ssid ?: "") ||
                !NetWorkUtil.isNetWorkAvaiable(context)
            ) && retryCount < RETRY_COUNT
        ) {
            return disConnectApWifiState(productId, retryCount + 1)
        }
        return false
    }

    private fun prepareFeedBack(
        questionDesc: String,
        contact: String?,
        productId: String,
        isSelectLog: Boolean
    ) = viewModelScope.launch {
        _feedbackLiveData.setState {
            copy(fetchStatus = FeedbackFetchStatus.FeedbackFetching)
        }
        when {
            isMijiaSSSeriesDevice(productId) -> {
                dealFeedBackAction(questionDesc, contact, productId, isSelectLog)
            }

            else -> {
                val preNetWorkAvaiable = NetWorkUtil.isNetWorkAvaiable(context)
                Timber.d("%s 提交反馈--->网络切换前是否可用--%s", FEED_BACK_LOG, preNetWorkAvaiable)
                while (isSelectLog && disConnectApWifiState(productId, 1)) {
                    Timber.d("%s 提交反馈--->网络切换中...", FEED_BACK_LOG)
                }
                checkAppNetState(isSelectLog) { afterNetWorkAvaiable ->
                    Timber.d(
                        "%s 提交反馈--->网络切换后是否可用--%s",
                        FEED_BACK_LOG,
                        afterNetWorkAvaiable
                    )
                    if (!afterNetWorkAvaiable) {
                        _feedbackLiveData.postState {
                            copy(fetchStatus = FeedbackFetchStatus.FeedbackNetErrorFailed)
                        }
                        return@checkAppNetState
                    }
                    dealFeedBackAction(questionDesc, contact, productId, isSelectLog)
                }
            }
        }
    }

    private fun dealFeedBackAction(
        questionDesc: String,
        contact: String?,
        productId: String,
        isSelectLog: Boolean
    ) = viewModelScope.launch {
        Timber.d("%s 提交反馈--->start", FEED_BACK_LOG)
        feedbackHandler.dealFeedBackAction(
            FeedBackData(
                questionDesc,
                contact,
                productId,
                isSelectLog,
                _feedbackLiveData.value?.photoList,
                _feedbackLiveData.value?.questionType?.questionType ?: 0,
                logPathList,
                miwearQuestionClassify = _feedbackLiveData.value?.miwearQuestionClassify
            )
        ).collect {
            when {
                it.isLoading() -> {
                    _feedbackLiveData.setState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbackFetching)
                    }
                }

                it.isSuccess() -> {
                    Timber.d("%s 提交反馈--->成功", FEED_BACK_LOG)
                    _feedbackLiveData.setState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbackSuccess)
                    }
                }

                it.isError() -> {
                    Timber.d("%s 提交反馈--->失败=%s", FEED_BACK_LOG, it.message)
                    _feedbackLiveData.setState {
                        copy(fetchStatus = FeedbackFetchStatus.FeedbackNetErrorFailed)
                    }
                }
            }
        }
    }

    private fun dispatchEvent(event: FeedbackEvent) {
        feedbackCallback.dispatchOnMainThread {
            invoke(event)
        }
    }

    override fun onCleared() {
        kotlin.runCatching {
            svDeviceLogDownloadHelper.stopDownloadRomLog()
            o95DeviceLogDownloadHelper.stopDownloadRomLog()
            handler.removeCallbacksAndMessages(null)
        }
        super.onCleared()
    }

//    private fun isMiWearFeedbackModel(model: String) : Boolean {
//        return isMijiaO95SeriesDevice(model) || model == ss2Model
//    }

    companion object {

        const val EMPTY_ITEM = "empty"
        private const val MAST_PHOTO = 3
        const val MIN_DESC_LENGTH = 6
        private const val FEED_BACK_LOG = "FEED_BACK_LOG"
        private const val DELAY = 2000L
        private const val RETRY_COUNT = 5
        private const val DEVICE_RETRY_COUNT = 10
        private const val APP_RETRY_COUNT = 2
        private const val CHECK_AVALIABLE_URL = "https://www.mi.com/"
        private const val O95_MODEL_ID = 680
        private const val O97_MODEL_ID = 679
        private const val CONNECT_DEVICE_TIME_OUT = 0x11
        private const val TIME_OUT_DELAY = 10_000L
    }
}
