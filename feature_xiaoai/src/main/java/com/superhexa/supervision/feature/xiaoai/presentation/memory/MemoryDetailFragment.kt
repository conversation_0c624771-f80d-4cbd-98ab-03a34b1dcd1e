package com.superhexa.supervision.feature.xiaoai.presentation.memory

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.OnBackPressedCallback
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.navigation.fragment.findNavController
import com.alibaba.android.arouter.facade.annotation.Route
import com.superhexa.supervision.feature.xiaoai.R
import com.superhexa.supervision.feature.xiaoai.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.SHOW_URL
import com.superhexa.supervision.library.base.basecommon.network.NetworkMonitor
import com.superhexa.supervision.library.base.basecommon.theme.ColorChatPageBg
import com.superhexa.supervision.library.base.presentation.dialog.LoadingDialog
import com.superhexa.supervision.library.base.webviewhelper.PageType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

/*
* 用来展示记忆停车位详情界面：展示内容由云端url中显示，客户端只需要实现点击×，返回上一个界面的功能
* */
@Route(path = RouterKey.Memory_MemoryDetail)
class MemoryDetailFragment : ShowLoadingFragment() {

    // 网页是否加载完成的标志
    private var isWebViewLoaded = mutableStateOf(false)
    private var canBack = mutableStateOf(false)
    private val reloadStatusViewModel: ReloadStatusViewModel by activityViewModels()
    private val loadingDialog: LoadingDialog by lazy { LoadingDialog() }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val url = arguments?.getString(SHOW_URL) ?: ""
        return ComposeView(requireContext()).apply {
            setContent {
                MemoryDetailScreen(url) {
                    findNavController().popBackStack()
                    reloadStatusViewModel.onImgRevisedChanged(true)
                }
            }
        }
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    findNavController().popBackStack()
                    reloadStatusViewModel.onImgRevisedChanged(true)
                }
            }
        )
    }

    @SuppressLint("SetJavaScriptEnabled")
    @Suppress("MagicNumber")
    @Composable
    fun MemoryDetailScreen(
        url: String,
        onCloseClick: () -> Unit
    ) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxSize()
                .background(color = ColorChatPageBg)
        ) {
            val (closeBar, webViewContent) = createRefs()
            // 一个删除栏
            Row(
                modifier = Modifier
                    .constrainAs(closeBar) {
                        top.linkTo(parent.top, 13.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
                    .fillMaxWidth()
                    .height(32.dp)
            ) {
                Image(
                    painter = painterResource(R.drawable.bt_close_memory_detial),
                    contentDescription = "Close",
                    modifier = Modifier
                        .padding(start = 22.dp)
                        .size(32.dp)
                        .clickable(onClick = onCloseClick)
                )
            }
            WebViewScreen(
                Modifier.constrainAs(webViewContent) {
                    top.linkTo(closeBar.bottom, 28.dp)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom)
                    height = Dimension.fillToConstraints
                },
                url
            )
        }
        if (!isWebViewLoaded.value) {
            showLoading()
        } else {
            hideLoading()
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    @Composable
    fun WebViewScreen(modifier: Modifier, url: String) {
        val context = LocalContext.current

        val webView = remember {
            WebView(context).apply {
                settings.javaScriptEnabled = true
                settings.domStorageEnabled = true
                settings.allowFileAccess = true
                settings.allowContentAccess = true
                settings.allowFileAccessFromFileURLs = true
                settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                isScrollContainer = true // 关键触摸事件配置
                requestFocusFromTouch() // 允许接收触摸事件
                webChromeClient = WebChromeClient()
                isFocusable = true // 允许获取焦点
                isFocusable = true // 允许获取焦点
                setFocusableInTouchMode(true) // 允许在触摸模式下获取焦点

                webViewClient = object : WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        isWebViewLoaded.value = true
                        Timber.d("onPageFinished")
                        hideLoading()
                    }
                }
            }
        }
        val memoryWebPageHelper = remember(webView) {
            MemoryWebPageHelper(webView, context, url).apply {
                initWebView(
                    object : MemoryWebInterfaceListener() {
                        override fun nativeToPage(url: String, pageType: String) {
                            if (NetworkMonitor.isNetworkValidated()) {
                                when (pageType) {
                                    PageType.MEMORY_PAGE.value ->
                                        lifecycleScope.launch(Dispatchers.Main) {
                                            HexaRouter.navigateToMemoryDetail(this@MemoryDetailFragment, url)
                                        }

                                    PageType.MEMORY_IMAGE_PAGE.value ->
                                        lifecycleScope.launch(Dispatchers.Main) {
                                            HexaRouter.navigateToMemoryImage(this@MemoryDetailFragment, url)
                                        }
                                }
                            }
                        }

                        override fun onContentLoadFinish() {
                            super.onContentLoadFinish()
                            isWebViewLoaded.value = true
                        }
                    }
                )
            }
        }

        Box(modifier = modifier) {
            if (isWebViewLoaded.value) {
                AndroidView(
                    factory = { webView },
                    modifier = modifier
                        .fillMaxWidth()
                        .fillMaxHeight()
                )
            }
        }
    }
}