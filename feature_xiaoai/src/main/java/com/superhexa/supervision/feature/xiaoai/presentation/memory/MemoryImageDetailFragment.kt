@file:Suppress("<PERSON><PERSON><PERSON><PERSON>", "EmptyFunctionBlock", "TooGenericExceptionCaught", "MagicN<PERSON>ber")
package com.superhexa.supervision.feature.xiaoai.presentation.memory

import android.content.Context
import android.graphics.Bitmap
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.compose.BackHandler
import androidx.compose.runtime.*
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.bumptech.glide.Glide
import com.superhexa.supervision.feature.xiaoai.presentation.chat.BaseHistoryViewModel
import com.superhexa.supervision.feature.xiaoai.presentation.chat.ChatHistoryEffect
import com.superhexa.supervision.feature.xiaoai.presentation.component.FullScreenImagePreview
import com.superhexa.supervision.feature.xiaoai.presentation.component.ImageState
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import me.drakeet.support.toast.ToastCompat
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
class MemoryImageDetailFragment : Fragment() {
    private val viewModel by viewModels<BaseHistoryViewModel>()
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(requireContext()).apply {
        setContent {
            val context = LocalContext.current
            val imageUrl = arguments?.getString(BundleKey.SHOW_URL).orEmpty()
            val imageState by produceState<ImageState>(ImageState.Initial) {
                value = ImageState.Loading
                val file = downloadToCache(context, imageUrl)
                value = if (file != null) ImageState.Success(file)
                else ImageState.Error("下载失败")
            }
            LaunchedEffect(Unit) {
                viewModel.mEffect.collect { effect ->
                    when (effect) {
                        is ChatHistoryEffect.ShowTips -> toastImpl(effect.msg)
                        is ChatHistoryEffect.Toast   -> toastImpl(getString(effect.stringResId))
                    }
                }
            }
            BackHandler { findNavController().popBackStack() }
            FullScreenImagePreview(
                imageState = imageState,
                onDismiss = { findNavController().popBackStack() },
                onSaveToGallery = {
                    if (imageState is ImageState.Success) {
                        viewModel.saveToGallery(
                            this@MemoryImageDetailFragment,
                            (imageState as ImageState.Success).imageFile
                        )
                    }
                }
            )
        }
    }

    private suspend fun downloadToCache(
        context: Context,
        url: String
    ): File? = withContext(Dispatchers.IO) {
        try {
            val dir = context.externalCacheDir ?: context.cacheDir
            val file = File(dir, "temp.jpg")
            val bitmap = Glide.with(context)
                .asBitmap()
                .load(url)
                .submit()
                .get()
            FileOutputStream(file).use {
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, it)
            }
            file
        } catch (e: Exception) {
            Timber.e(e, "downloadToCache failed")
            null
        }
    }
    private fun toastImpl(message: String) {
        ToastCompat.makeText(
            requireContext(),
            message,
            Toast.LENGTH_SHORT
        ).show()
    }
}

