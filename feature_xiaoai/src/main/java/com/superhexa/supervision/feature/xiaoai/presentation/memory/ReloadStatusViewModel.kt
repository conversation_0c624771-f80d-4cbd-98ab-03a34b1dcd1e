package com.superhexa.supervision.feature.xiaoai.presentation.memory

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel

class ReloadStatusViewModel : ViewModel() {
    private val _isReloadStatusChanged = MutableLiveData<Boolean>(false)
    val isReloadStatusChanged: LiveData<Boolean> get() = _isReloadStatusChanged
    private var currentStatus: Boolean = false

    fun onImgRevisedChanged(isChanged: Boolean) {
        if (currentStatus != isChanged) {
            currentStatus = isChanged
            _isReloadStatusChanged.value = isChanged
        } else {
            // 即使值相同，也强制更新 LiveData
            _isReloadStatusChanged.postValue(isChanged)
        }
    }
}