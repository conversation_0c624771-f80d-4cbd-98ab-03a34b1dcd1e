package com.superhexa.supervision.feature.xiaoai.track

import com.superhexa.music.utils.LiteJsonUtils.toJson
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.config.ConfigCache
import com.xiaomi.aivs.track.CrossDeviceControlEventParams
import com.xiaomi.aivs.track.EventTrackKv
import com.xiaomi.aivs.track.RecordEventParams
import com.xiaomi.aivs.track.SummaryEventParams
import com.xiaomi.aivs.track.UploadImageQAEventParams
import okhttp3.internal.toImmutableMap
import timber.log.Timber

object EventTrackHelper {

    private const val APP_ID = "31000402132"
    private var commonParams = mutableMapOf<String, String>()

    init {
        commonParams["tip"] = "1634.4.1.1.40313"
    }

    fun appendCommonParams(key: String, value: String) {
        Timber.d("appendCommonParams:$key,$value")
        commonParams[key] = value
    }

    // tip不属于公参的范畴，目前小爱相关的打点，如果有需要打到别的tip，需要在params里加上需要的tip
    // 为了减小对已有打点的影响，不强制要求所有调用都加上tip参数，带上的自动替换，不带的用commonParams里的
    fun doEventTrack(
        eventName: String,
        params: Map<String, Any>?,
        isNeedToJson: Boolean = false
    ) {
        Timber.d("doEventTrack:$eventName,$params")
        val containsTip: Boolean = params?.containsKey(EventCons.EVENT_KEY_TIP) ?: false
        val modifiedParams = if (containsTip) {
            val paramsMap = commonParams.toMutableMap()
            paramsMap.entries.removeIf { it.key.contains(EventCons.EVENT_KEY_TIP) }
            paramsMap
        } else { commonParams }

        val eventMap = mutableMapOf<String, Any>().apply {
            putAll(modifiedParams)
            put(EventTrackKv.SRV_ENV.point(), ConfigCache.envDomainStr())
            put(EventTrackKv.UUID.point(), AiSpeechEngine.INSTANCE.requestId())
        }
        val safeParams = params?.toImmutableMap()
        safeParams?.let {
            if (!containsTip || isNeedToJson) {
                // 原逻辑, 后续修改还要根据是否需要转换为json看一下
                eventMap.put("key_value", it.toJson())
            } else {
                eventMap.putAll(it)
            }
        }
        StatisticHelper.deviceTrack(APP_ID, eventName, eventMap)

        when (eventMap[EventCons.EVENT_KEY_TIP]) {
            UploadImageQAEventParams.EVENT_IMAGE_UPLOAD_TIP -> {
                Timber.d("trackImageQAEvent: eventName -> $eventName, params -> $eventMap")
            }

            CrossDeviceControlEventParams.EVENT_CROSS_DEVICE_CONTROL_TIP -> {
                Timber.d("trackCrossDeviceControlEvent: eventName -> $eventName, params -> $eventMap")
            }

            RecordEventParams.EVENT_Record_TIP -> {
                Timber.d("trackRecordEvent: eventName -> $eventName, params -> $eventMap")
            }

            SummaryEventParams.EVENT_SUMMARY_TIP -> {
                Timber.d("trackSummaryEvent: eventName -> $eventName, params -> $eventMap")
            }

            else -> Unit
        }
    }
}
