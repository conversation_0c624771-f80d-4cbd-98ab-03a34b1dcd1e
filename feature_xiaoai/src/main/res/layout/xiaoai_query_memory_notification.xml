<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/query_memory_notification_view">

    <!-- 左边的图标 -->
    <ImageView
        android:id="@+id/icon"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:src="@drawable/ic_memory_push" />

    <!-- 标题 -->
    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:layout_marginStart="12dp"
        android:layout_toEndOf="@id/icon"
        android:fontFamily="@font/misanslatin_bold"
        android:text="@string/xiaoai_memory_push_title"
        android:textColor="@color/black_90"
        android:textSize="18sp" />

    <!-- 内容 -->
    <TextView
        android:id="@+id/content"
        android:layout_width="wrap_content"
        android:layout_height="19dp"
        android:layout_toEndOf="@id/icon"
        android:layout_marginStart="12dp"
        android:layout_below="@id/title"
        android:textColor="@color/black_70"
        android:text="@string/record_event_connect_desc"
        android:textSize="14sp" />

    <!-- 删除按钮 -->
    <ImageView
        android:id="@+id/delete_button"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:contentDescription="@string/libs_delete"
        android:src="@drawable/bt_close_memory_push" />
</RelativeLayout>