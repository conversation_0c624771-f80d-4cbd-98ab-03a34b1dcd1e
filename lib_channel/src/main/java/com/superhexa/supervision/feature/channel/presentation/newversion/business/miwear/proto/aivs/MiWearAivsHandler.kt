package com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.aivs

import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.GetSystemSettings
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.RequestAivsMultiModal
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SyncAivsStatus
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SyncInstructionList
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.SyncInstructionList.InstructionData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.COLLABORATION_WAKE_UP
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.VOICE_WAKE_UP
import com.superhexa.supervision.library.base.basecommon.extension.LifecycleCallback
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.basecommon.tools.resumeCheckIsCompleted
import com.superhexa.supervision.library.base.tools.CoroutineBase
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.utils.ExtensionFun.getOrNull
import com.xiaomi.fitness.device.contact.export.IDeviceSyncCallback
import com.xiaomi.fitness.device.contact.export.SyncResult
import com.xiaomi.wear.protobuf.nano.AivsProtos
import com.xiaomi.wear.protobuf.nano.WearProtos
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import timber.log.Timber
import java.util.UUID
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

/**
 * 类描述:
 * 创建日期: 2024/10/8 on 11:17
 * 作者: qintaiyuan
 */
object MiWearAivsHandler : CoroutineBase() {
    private const val TAG = "MiWearAivsHandler_TAG"
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private fun getClient(): IDeviceOperator<O95StateLiveData>? {
        return if (DeviceModelManager.isMijiaO95SeriesDevice(bondDevice?.model)) {
            DecoratorUtil.getDecorator(bondDevice)
        } else {
            null
        }
    }

    val startTtsCallback: LifecycleCallback<(String) -> Unit> =
        LifecycleCallback()

    private val lastVoiceStatus = AtomicInteger(0)
    private val isVoiceWakeUp = AtomicBoolean(false)

    fun getLastVoiceStatus() = lastVoiceStatus.get()

    suspend fun getAIAssistantData() = suspendCancellableCoroutine { continuation ->
        val decorator = getClient()
        decorator?.sendMiWearCommand(
            GetSystemSettings(GetSystemSettings.ai),
            object : IDeviceSyncCallback.Stub() {
                override fun onSyncSuccess(did: String?, type: Int, result: SyncResult?) {
                    result?.packet?.system?.systemSetting?.let {
                        syncAiAssistant(
                            it.aiAssistant?.voiceWakeup ?: false,
                            it.aiAssistant?.collaborationWakeup ?: false
                        )
                        Timber.d("getAIAssistantData----data=$it")
                    }
                    // 在回调成功时恢复挂起函数的执行，并返回结果
                    continuation.resumeCheckIsCompleted(true, null)
                }

                override fun onSyncError(did: String?, type: Int, code: Int) {
                    Timber.d("getAIAssistantData----onSyncError=$code")
                    // 在回调成功时恢复挂起函数的执行，并返回结果
                    continuation.resumeCheckIsCompleted(false, null)
                }
            }
        )
    }

    fun syncAiAssistant(
        voiceWakeup: Boolean,
        collaborationWakeup: Boolean
    ) {
        MMKVUtils.encode(VOICE_WAKE_UP, voiceWakeup)
        MMKVUtils.encode(COLLABORATION_WAKE_UP, collaborationWakeup)
    }

    fun onReceivedAivsStatusData(did: String, packet: WearProtos.WearPacket) {
        Timber.tag(TAG)
            .d("onReceivedAivsStatusData:$did,$packet，MiWearAivsHandler=${this.hashCode()}")
        syncAivsStatus(
            AiSpeechEngine.INSTANCE.dialogState(),
            AiSpeechEngine.INSTANCE.engineState(),
            AiSpeechEngine.INSTANCE.ttsState()
        )
    }

    fun onReceivedSyncInstructionListData(did: String, packet: WearProtos.WearPacket) {
        val instruction = packet.aivs?.instructionList?.list?.firstOrNull()
        Timber.tag(TAG).d(
            "onReceivedSyncInstructionListData:" +
                "$did,$packet,${instruction?.resultType} MiWearAivsHandler=${this.hashCode()}"
        )
        when (instruction?.resultType) {
            // 唤醒(废弃，改为wakeup).
//            AivsProtos.START_LISTENING -> AiSpeechEngine.INSTANCE.postSpeechBegin()
            // 长按眼镜touch1.5s退出
            AivsProtos.STOP_CAPTURE -> {
                val reason = "收到固件指令:STOP_CAPTURE"
                // BUGFIX GTKGLASS-13092
                AiSpeechEngine.INSTANCE.pauseMediaPlayer(reason = reason)
                AiSpeechEngine.INSTANCE.finishSession(reason = reason)
            }
        }
    }

    fun onReceivedWakeupEventData(did: String, packet: WearProtos.WearPacket) {
        val requestInfo = packet.aivs?.wakeupEvent?.requestInfo
        Timber.d("onReceivedWakeupEventData:cmd:${packet.aivs.wakeupEvent.cmd}")
        requestInfo?.let {
            val requestId = String(it.requestId)
            val transactionId = String(it.transactionId)
            Timber.tag(TAG).d("onReceivedWakeupEventData:$did,$packet")
            Timber.tag(TAG).d("onReceivedWakeupEventData:$requestId,$transactionId")
            isVoiceWakeUp.set(true)
            AiSpeechEngine.INSTANCE.onReceiveRequestId(requestId, transactionId,packet.aivs.wakeupEvent.cmd)
        } ?: run {
            Timber.tag(TAG).w("requestInfo is null.")
        }
    }

    fun onReceivedStartTTSEventData(did: String, packet: WearProtos.WearPacket) {
        val message = packet.aivs?.ttsRequest?.message
        message?.let {
            startTtsCallback.dispatchOnMainThread {
                invoke(message)
                Timber.tag(TAG).d("onReceivedStartTTSEventData:$did,$message,$packet")
            }
        } ?: run {
            Timber.tag(TAG).w("message is null.")
        }
    }

    @Suppress("MaxLineLength")
    fun requestAivsMultiModal(transactionId: String, requestId: String, payload: String?, cmd: Int) =
        launch {
            val decorator = getClient()
            Timber.tag(TAG)
                .d("requestAivsMultiModal- transId=$transactionId,reqId=$requestId,cmd=$cmd,MiWearAivsHandler=${this.hashCode()},clien=$decorator")
            decorator?.sendMiWearCommand(
                RequestAivsMultiModal(transactionId, requestId, payload, cmd),
                object : IDeviceSyncCallback.Stub() {
                    override fun onSyncSuccess(did: String?, type: Int, result: SyncResult?) {
                        val success = result?.isSuccess ?: false
                        if (success) {
                            Timber.tag(TAG).d("requestAivsMultiModal成功")
                        } else {
                            Timber.tag(TAG).d("requestAivsMultiModal失败")
                        }
                    }

                    override fun onSyncError(did: String?, type: Int, code: Int) {
                        Timber.tag(TAG).d("requestAivsMultiModal失败-did=$did,type=$type,cod=$code")
                    }
                }
            )
        }

    @Suppress("MaxLineLength")
    fun syncAivsStatus(voiceStatus: Int, deviceStatus: Int, ttsStatus: Int) = launch {
        val decorator = getClient()
        Timber.tag(TAG)
            .d("syncAivsStatus--voiceStatus=$voiceStatus,deviceStatus=$deviceStatus,MiWearAivsHandler=${this.hashCode()},clien=$decorator")
        lastVoiceStatus.set(voiceStatus)
        decorator?.sendMiWearCommand(
            SyncAivsStatus(voiceStatus, deviceStatus, ttsStatus),
            object : IDeviceSyncCallback.Stub() {
                override fun onSyncSuccess(did: String?, type: Int, result: SyncResult?) {
                    val success = result?.isSuccess ?: false
                    if (success) {
                        Timber.tag(TAG).d("syncAivsStatus成功")
                    } else {
                        Timber.tag(TAG).d("syncAivsStatus失败")
                    }
                }

                override fun onSyncError(did: String?, type: Int, code: Int) {
                    Timber.tag(TAG).d("syncAivsStatus-did=$did,type=$type,cod=$code")
                }
            }
        )
    }

    @Suppress("MaxLineLength")
    fun sendAivsInstruction(instructionDate: InstructionData) = launch {
        val decorator = getClient()
        Timber.tag(TAG)
            .d("sendAivsInstruction,MiWearAivsHandler=${this.hashCode()}, instructionDate = $instructionDate,clien=$decorator")
        decorator?.sendMiWearCommand(
            SyncInstructionList(instructionDate),
            object : IDeviceSyncCallback.Stub() {
                override fun onSyncSuccess(did: String?, type: Int, result: SyncResult?) {
                    val success = result?.isSuccess ?: false
                    if (success) {
                        Timber.tag(TAG).d("sendAivsInstruction成功")
                    } else {
                        Timber.tag(TAG).d("sendAivsInstruction失败")
                    }
                }

                override fun onSyncError(did: String?, type: Int, code: Int) {
                    Timber.tag(TAG).d("sendAivsInstruction失败-did=$did,type=$type,cod=$code")
                }
            }
        )
    }

    fun sendAivsErrorExitInstruction(errorCode: Int, errorMsg: String) {
        if (isVoiceWakeUp.getAndSet(false)) {
            val dialogId = UUID.randomUUID().toString()
            val exitInstruction = dialogId?.let {
                AiSpeechEngine.INSTANCE.generateSysExceptionInstruction(
                    errorCode,
                    errorMsg,
                    it
                )
            }

            AiSpeechEngine.INSTANCE.onTextResponseSynthesizer(
                exitInstruction?.header?.transactionId?.getOrNull(),
                dialogId,
                errorMsg,
                true,
                instructionJson = exitInstruction.toString()
            )
        }
    }
}
