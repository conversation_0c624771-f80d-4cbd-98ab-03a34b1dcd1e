@file:Suppress("TooGenericExceptionCaught", "Magic<PERSON><PERSON><PERSON>")

package com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.voice

import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.MiWearRecordHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.voice.VoiceMassData.HCWAIVSMassCommand
import com.superhexa.supervision.feature.channel.presentation.newversion.tools.SBCCodec
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.tools.CoroutineBase
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.data.DialogState
import com.xiaomi.aivs.engine.helper.ImageFileHandler
import com.xiaomi.aivs.engine.helper.ImageTransferListener
import com.xiaomi.aivs.engine.proxy.SpeechEngineProxyImpl.Companion.WakeUpCmd
import com.xiaomi.aivs.track.EventTrack
import com.xiaomi.aivs.track.EventTrackKv
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import timber.log.Timber

/**
 * 类描述:
 * 创建日期: 2024/9/21 on 14:57
 * 作者: qintaiyuan
 */
class MiWearVoiceHandler : CoroutineBase() {

    private var imageRequestId: String = ""

    // 唤醒数据.
    private var wakeupTotalSize: Int = 0
    private var wakeupDataSize: Int = 0
    private val imageTransferListeners = mutableMapOf<String, ImageTransferListener>()

    // 图片请求ID与类型关联表
    private val imageRequestMap = mutableMapOf<String, ImageFileHandler.ImageType>()
    private val mutex = Mutex()

    @Suppress("ComplexMethod", "LongMethod")
    fun onReceivedVoiceData(did: String, data: ByteArray) = launch(Dispatchers.IO) {
        mutex.withLock {
            val hcwaivsMassData = VoiceMassData(data)
            Timber.d("onReceivedVoiceData---did=$did,voiceType=${hcwaivsMassData.command}, data-size=${data.size}")
            when (hcwaivsMassData.command) {
                HCWAIVSMassCommand.UNKNOWN -> { // 未知
                }

                HCWAIVSMassCommand.START -> { // 语音开始
                    EventTrack.onEventTrackTime(key = EventTrackKv.ASR_OPEN_MIC)
                }

                HCWAIVSMassCommand.DATA -> { // 语音数据
                    val contentData = hcwaivsMassData.contentData
                    Timber.d("hcwaivsMassData.type=${hcwaivsMassData.type}")
                    when (hcwaivsMassData.type) {
                        VoiceMassData.HCWAIVSMassType.FIRST_VOICE -> {
                            AiSpeechEngine.INSTANCE.postSpeechBegin()
                            AiSpeechEngine.INSTANCE.startFirstVoice()
                            decodeSpeechData(contentData)?.let {
                                AiSpeechEngine.INSTANCE.postSpeechData(it, 0, it.size, false)
                            }
                        }

                        VoiceMassData.HCWAIVSMassType.FOLLOW_VOICE -> {
                            decodeSpeechData(contentData)?.let {
                                AiSpeechEngine.INSTANCE.postSpeechData(it, 0, it.size, false)
                            }
                        }

                        // 图片数据处理
                        // 这里给的是FirstImage 对应到Cropped
                        in imageTypeMapping.keys -> {
                            imageTypeMapping[hcwaivsMassData.type]?.let { type ->
                                startImageDataTransfer(hcwaivsMassData, type)
                            }
                        }

                        VoiceMassData.HCWAIVSMassType.FOLLOW_IMAGE -> {
                            val requestId = getActiveImageRequestId()
                            val type =
                                imageRequestMap[requestId] ?: ImageFileHandler.ImageType.UNKNOWN
                            AiSpeechEngine.INSTANCE.postImageData(
                                requestId,
                                contentData,
                                type = type,
                                postEnd = {
                                    Timber.d("postEnd,${imageTransferListeners[requestId]}")
                                    cleanupImageRequest(requestId)
                                    imageTransferListeners[requestId]?.onImageTransferComplete(
                                        requestId
                                    )
                                }
                            )
                        }

                        VoiceMassData.HCWAIVSMassType.IMAGE_HD_FOLLOW -> {
                            val requestId = getActiveImageRequestId()
                            val type =
                                imageRequestMap[requestId] ?: ImageFileHandler.ImageType.UNKNOWN
                            AiSpeechEngine.INSTANCE.postImageData(
                                requestId,
                                contentData,
                                type = type,
                                postEnd = {
                                    Timber.d("postEnd,${imageTransferListeners[requestId]}")
                                    cleanupImageRequest(requestId)
                                    imageTransferListeners[requestId]?.onImageStateChange(false)
                                    imageTransferListeners[requestId]?.onImageTransferComplete(
                                        requestId
                                    )
                                }
                            )
                        }

                        VoiceMassData.HCWAIVSMassType.IMAGE_OCR -> {
                            AiSpeechEngine.INSTANCE.postImageOcrData(
                                totalSize = hcwaivsMassData.totalLength,
                                hcwaivsMassData.contentData
                            )
                        }

                        VoiceMassData.HCWAIVSMassType.WAKEUP_VOICE_FIRST -> {
                            if (AiSpeechEngine.INSTANCE.dialogState() == DialogState.VOICE_IDLE) {
                                postWakeUpData(true, hcwaivsMassData.totalLength, contentData)
                            } else {
                                Timber.w("it is in continuous dialog.")
                            }
                        }

                        VoiceMassData.HCWAIVSMassType.WAKEUP_VOICE_FOLLOW -> {
                            if (AiSpeechEngine.INSTANCE.dialogState() == DialogState.VOICE_IDLE) {
                                postWakeUpData(false, hcwaivsMassData.totalLength, contentData)
                            } else {
                                Timber.w("it is in continuous dialog.")
                            }
                        }

//                        VoiceMassData.HCWAIVSMassType.SMALL_PICTURE_OF_CONVERSATION_RECORD -> {
//                            currentImgType = ImageFileHandler.ImageType.THUMBNAIL
//                            startReceivedImageData(hcwaivsMassData)
//                        }
//
//                        VoiceMassData.HCWAIVSMassType.LARGE_PICTURE_OF_CONVERSATION_RECORD -> {
//                            currentImgType = ImageFileHandler.ImageType.ORIGINAL
//                            startReceivedImageData(hcwaivsMassData)
//                        }

                        else -> {}
                    }
                }

                HCWAIVSMassCommand.END -> {
                    // AiSpeechEngine.INSTANCE.postSpeechEnd(requestId)
                }

                HCWAIVSMassCommand.QUIT -> { // 语音结束，退出
                }

                HCWAIVSMassCommand.ORDINARY_AUDIO_RECORDING_DATA,
                HCWAIVSMassCommand.AUDIO_RECORDING_DATA,
                HCWAIVSMassCommand.REAL_TIME_TRANSLATION_DATA,
                HCWAIVSMassCommand.SPEECH_HUB_DATA,
                HCWAIVSMassCommand.VOICE_PRINT_DATA -> {
                    MiWearRecordHandler.onReceivedMassRecordData(hcwaivsMassData.command, did, data)
                }
            }
        }
    }

    private fun startImageDataTransfer(
        hcwaivsMassData: VoiceMassData,
        type: ImageFileHandler.ImageType
    ) {
        try {
            val requestId = AiSpeechEngine.INSTANCE.postImageBegin(
                hcwaivsMassData.totalLength,
                hcwaivsMassData.contentLength.toInt(),
                type,
                getActiveRequestId()
            ).takeIf { it.isNotEmpty() } ?: run {
                Timber.e("Failed to generate image request ID")
                return
            }

            imageRequestMap.clear()
            imageRequestMap[requestId] = type
            AiSpeechEngine.INSTANCE.postImageData(
                requestId,
                hcwaivsMassData.contentData,
                type = type
            )
            imageTransferListeners[requestId]?.onImageTransferStart(requestId)
            // 设置超时清理
            // setupImageTransferTimeout(requestId)
        } catch (e: Exception) {
            Timber.e(e, "Error starting image transfer")
        }
    }

    private fun setupImageTransferTimeout(requestId: String) {
        // 设置30秒超时，防止长时间未完成的传输
        launch(Dispatchers.IO) {
            delay(30_000L)
            if (imageRequestMap.containsKey(requestId)) {
                Timber.w("Image transfer timeout for request: $requestId")
                cleanupImageRequest(requestId)
            }
        }
    }

    private fun cleanupImageRequest(requestId: String) {
        imageRequestMap.remove(requestId)
        // 可以添加其他清理逻辑，如通知引擎取消传输等
    }

    private fun getActiveImageRequestId(): String {
        return imageRequestMap.keys.firstOrNull() ?: run {
            Timber.w("No active image request for FOLLOW_IMAGE")
            ""
        }
    }

    private fun getActiveRequestId(): String {
        return imageTransferListeners.keys.firstOrNull() ?: run {
            Timber.w("No active image request for FOLLOW_IMAGE")
            ""
        }
    }

    private val imageTypeMapping = mapOf(
        VoiceMassData.HCWAIVSMassType.SMALL_PICTURE_OF_CONVERSATION_RECORD to ImageFileHandler.ImageType.THUMBNAIL,
        VoiceMassData.HCWAIVSMassType.FIRST_IMAGE to ImageFileHandler.ImageType.CROPPED,
        VoiceMassData.HCWAIVSMassType.LARGE_PICTURE_OF_CONVERSATION_RECORD to ImageFileHandler.ImageType.ORIGINAL
    )

    fun registerImageListener(requestId: String, listener: ImageTransferListener) {
        Timber.d("registerImageListener:$requestId")
        imageTransferListeners[requestId] = listener
    }

    fun unregisterImageListener(requestId: String) {
        Timber.d("unregisterImageListener:$requestId")
        imageTransferListeners.remove(requestId)
    }

    private fun postWakeUpData(isFirst: Boolean, wakeupTotalSize: Int, contentData: ByteArray?) {
        Timber.d("postWakeUpData:$isFirst,$wakeupTotalSize,${contentData?.size}")
        var isVoiceWakeUpEnabled: Boolean =
            MMKVUtils.decodeBoolean(ConstsConfig.UserVoiceWakeUpAgreement)
        if (isFirst) {
            wakeupDataSize = 0
        }
        wakeupDataSize += contentData?.size ?: 0
        when (AiSpeechEngine.INSTANCE.getRequestCmd()) {
            WakeUpCmd.WAKE_UP_REAL.cmd -> {
                if (isFirst) {
                    AiSpeechEngine.INSTANCE.postWakeupBegin(wakeupTotalSize)
                }
                decodeSpeechData(contentData, true)?.let {
                    Timber.d("isVoiceWakeUpEnabled:$isVoiceWakeUpEnabled")
                    if (isVoiceWakeUpEnabled) {
                        AiSpeechEngine.INSTANCE.postWakeupData(it, 0, it.size)
                    } else {
                        Timber.d("唤醒音频上传已关闭，postWakeupVoiceData被拦截")
                    }
                }
                if (wakeupDataSize == wakeupTotalSize) {
                    AiSpeechEngine.INSTANCE.postWakeupEnd()
                    AiSpeechEngine.INSTANCE.clearRequestCmd()
                }
            }

            WakeUpCmd.WAKE_UP_SUSPECT.cmd -> {
                decodeSpeechData(contentData, true)?.let {
                    Timber.d("isVoiceWakeUpEnabled:$isVoiceWakeUpEnabled")
                    if (isVoiceWakeUpEnabled) {
                    AiSpeechEngine.INSTANCE.uploadSuspectVoiceData(
                        "xiaomi", "",
                        it
                    ) } else {
                        Timber.d("唤醒音频上传已关闭，postWakeupVoiceData被拦截")
                    }
                }
                if (wakeupDataSize == wakeupTotalSize) {
                    AiSpeechEngine.INSTANCE.clearRequestCmd()
                }
            }
            else ->{
                Timber.d("post WakeupData Failed: receive Invalid Wakeup voice data")
            }
        }
    }

    private fun decodeSpeechData(data: ByteArray?, wakeup: Boolean = false): ByteArray? {
        Timber.d("decodeSpeechData:${data?.size},$wakeup")
        return data?.let { SBCCodec.decodeSBC(it) }
    }

    companion object {
        val INSTANCE: MiWearVoiceHandler by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            MiWearVoiceHandler()
        }
    }
}
