@file:Suppress("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>ength")

package com.superhexa.supervision.library.base.basecommon.compose

import android.app.Dialog
import android.graphics.drawable.ColorDrawable
import android.view.ViewGroup
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.annotation.DrawableRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.AnimatedVisibilityScope
import androidx.compose.animation.core.AnimationConstants
import androidx.compose.animation.core.MutableTransitionState
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.window.Popup
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.lifecycle.setViewTreeLifecycleOwner
import androidx.savedstate.setViewTreeSavedStateRegistryOwner
import com.airbnb.lottie.LottieComposition
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieConstants
import com.superhexa.supervision.library.base.R
import com.superhexa.supervision.library.base.basecommon.compose.hexaslider.HexaSliderWithLabel
import com.superhexa.supervision.library.base.basecommon.extension.clickDebounce
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color222425_30
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack50
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_14
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_202
import com.superhexa.supervision.library.base.basecommon.theme.Dp_22
import com.superhexa.supervision.library.base.basecommon.theme.Dp_25
import com.superhexa.supervision.library.base.basecommon.theme.Dp_26
import com.superhexa.supervision.library.base.basecommon.theme.Dp_260
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_4
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_5
import com.superhexa.supervision.library.base.basecommon.theme.Dp_60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_62
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Dp_80
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.SelectItem
import com.superhexa.supervision.library.base.data.model.SelectItemParams
import kotlinx.coroutines.delay
import timber.log.Timber

@Composable
private fun AnimatedSheetBg(visible: Boolean) {
    AnimatedVisibility(
        visible = visible,
        enter = fadeIn(animationSpec = tween()),
        exit = fadeOut(animationSpec = tween())
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(ColorBlack50)
        )
    }
}

@Composable
private fun AnimatedSheetContent(
    visible: Boolean,
    content: @Composable AnimatedVisibilityScope.() -> Unit
) {
    val state = remember { MutableTransitionState(false) }.apply { targetState = visible }
    AnimatedVisibility(
        visibleState = state,
        modifier = Modifier,
        enter = slideInVertically(
            initialOffsetY = { height -> height },
            animationSpec = TweenSpec()
        ),
        exit = slideOutVertically(
            targetOffsetY = { height -> height },
            animationSpec = TweenSpec()
        ),
        content = content
    )
}

@Composable
fun BottomSheetPopup(
    modifier: Modifier = Modifier,
    visible: Boolean,
    onDismiss: () -> Unit,
    content: @Composable () -> Unit
) {
    val onBackPressed = { onDismiss() }
    BackHandler(enabled = visible, onBack = onBackPressed)
    Timber.d("BottomSheet isShow $visible ")
    var showPopup by remember { mutableStateOf(false) }
    LaunchedEffect(visible) {
        showPopup = if (visible) {
            true
        } else {
            delay(AnimationConstants.DefaultDurationMillis.toLong())
            false
        }
    }
    if (showPopup) {
        Popup(alignment = Alignment.BottomCenter, onDismissRequest = {}) {
            AnimatedSheetBg(visible)
            AnimatedSheetContent(visible) {
                BoxWithConstraints(
                    modifier = modifier.fillMaxSize(),
                    contentAlignment = Alignment.BottomCenter
                ) {
                    Card(
                        shape = RoundedCornerShape(topStart = Dp_16, topEnd = Dp_16),
                        backgroundColor = Color18191A,
                        elevation = Dp_0,
                        modifier = Modifier
                            .fillMaxWidth()
                            .wrapContentHeight()
                    ) { content() }
                }
            }
        }
    }
}

/**
 * 解决折叠屏及画中画时底部弹框显示错位的问题
 * 使用 android.app.Dialog 替换 compose Popup
 * @param modifier 修饰符
 * @param visible 是否显示弹窗
 * @param onDismiss 弹窗消失回调
 * @param content 弹窗内容
 */
@Composable
fun BottomSheet(
    modifier: Modifier = Modifier,
    visible: Boolean,
    onDismiss: () -> Unit,
    content: @Composable () -> Unit
) {
    val context = LocalContext.current
    var showDialog by remember { mutableStateOf(false) }

    LaunchedEffect(visible) {
        if (visible) {
            showDialog = true
        } else {
            showDialog = false
        }
    }

    if (showDialog) {
        DisposableEffect(Unit) {
            val dialog = Dialog(context).apply {
                setContentView(
                    ComposeView(context).apply {
                        val activity = context as? ComponentActivity
                        activity?.let {
                            setViewTreeLifecycleOwner(it)
                            setViewTreeSavedStateRegistryOwner(it)
                        }
                        setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                        setContent {
                            Box(modifier = Modifier.fillMaxSize()) {
                                AnimatedSheetBg(visible) // 用 internalVisible 控制动画
                                AnimatedSheetContent(visible) {
                                    BoxWithConstraints(
                                        modifier = modifier.fillMaxSize(),
                                        contentAlignment = Alignment.BottomCenter
                                    ) {
                                        Card(
                                            shape = RoundedCornerShape(topStart = Dp_16, topEnd = Dp_16),
                                            backgroundColor = Color18191A,
                                            elevation = Dp_0,
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .wrapContentHeight()
                                                .clickable(
                                                    indication = null,
                                                    interactionSource = remember { MutableInteractionSource() }
                                                ) {
                                                    // 禁止事件穿透
                                                }
                                        ) { content() }
                                    }
                                }
                            }
                        }
                    }
                )
                window?.apply {
                    setBackgroundDrawable(ColorDrawable(android.graphics.Color.TRANSPARENT))
                    setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
                    setFlags(
                        WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                        WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                    )
                }
                setCancelable(true)
                setCanceledOnTouchOutside(false)
                setOnCancelListener { onDismiss() }
                setOnDismissListener { onDismiss() }
            }

            if (visible) dialog.show()

            onDispose {
                if (dialog.isShowing) dialog.dismiss()
            }
        }
    }
}

/**
 * ------------------------ 底部弹窗样式 ------------------------
 */

/**
 * 标题+描述+底部两个按钮
 * @param title 标题
 * @param des 描述不传不显示（可不传）
 * @param visible 弹窗是否显示
 * @param buttonConfig 传入1个ButtonParams ButtonConfig.OneButton。
 * @param onDismiss 弹窗消失回调
 */
@Composable
fun BottomSheetTitleDesOneButton(
    title: String? = null,
    des: String? = null,
    visible: Boolean = false,
    buttonConfig: ButtonConfig.OneButton,
    onDismiss: (() -> Unit)? = null
) {
    BottomSheet(visible = visible, onDismiss = { onDismiss?.invoke() }) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = Dp_260)
        ) {
            val (titleDesRef, rowRef) = createRefs()
            TitleDes(
                title = title,
                des = des,
                modifier = Modifier
                    .constrainAs(titleDesRef) {
                        top.linkTo(parent.top, margin = Dp_30)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        bottom.linkTo(rowRef.top, margin = Dp_20)
                    }
            )
            Row(
                modifier = Modifier
                    .constrainAs(rowRef) {
                        bottom.linkTo(parent.bottom, margin = Dp_30)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            ) {
                SubmitButton(
                    subTitle = buttonConfig.button.text,
                    textColor = buttonConfig.button.textColor,
                    enable = true,
                    enableColors = buttonConfig.button.enableColors,
                    disableColors = buttonConfig.button.disableColors,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_28, end = Dp_28)
                ) {
                    onDismiss?.invoke(); buttonConfig.button.onClick?.invoke()
                }
            }
        }
    }
}

/**
 * 标题+描述+底部两个按钮
 * @param title 标题
 * @param des 描述不传不显示（可不传）
 * @param imageId 图片id
 * @param visible 弹窗是否显示
 * @param buttonConfig 传入1个ButtonParams ButtonConfig.OneButton。
 * @param onDismiss 弹窗消失回调
 */
@Composable
fun BottomSheetTitleDesImageOneButton(
    title: String? = null,
    des: String? = null,
    @DrawableRes imageId: Int,
    visible: Boolean = false,
    buttonConfig: ButtonConfig.OneButton,
    onDismiss: (() -> Unit)? = null
) {
    BottomSheet(visible = visible, onDismiss = { onDismiss?.invoke() }) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = Dp_260)
        ) {
            val (imageIcon, titleDesRef, rowRef) = createRefs()
            Image(painter = painterResource(imageId),
                contentDescription = null,
                modifier = Modifier
                .size(Dp_60)
                .constrainAs(imageIcon) {
                    top.linkTo(parent.top, margin = Dp_30)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                })
            TitleDes(
                title = title,
                des = des,
                modifier = Modifier
                    .constrainAs(titleDesRef) {
                        top.linkTo(imageIcon.bottom, margin = Dp_20)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        bottom.linkTo(rowRef.top, margin = Dp_20)
                    }
            )
            Row(
                modifier = Modifier
                    .constrainAs(rowRef) {
                        bottom.linkTo(parent.bottom, margin = Dp_30)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            ) {
                SubmitButton(
                    subTitle = buttonConfig.button.text,
                    textColor = buttonConfig.button.textColor,
                    enable = true,
                    enableColors = buttonConfig.button.enableColors,
                    disableColors = buttonConfig.button.disableColors,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_28, end = Dp_28)
                ) {
                    onDismiss?.invoke(); buttonConfig.button.onClick?.invoke()
                }
            }
        }
    }
}

/**
 * 标题+描述+底部两个按钮
 * @param title 标题
 * @param des 描述不传不显示（可不传）
 * @param visible 弹窗是否显示
 * @param buttonConfig 传入2个ButtonParams ButtonConfig.TwoButton。
 * @param onDismiss 弹窗消失回调
 */
@Composable
fun BottomSheetTitleDes2Button(
    title: String? = null,
    des: String? = null,
    visible: Boolean = false,
    buttonConfig: ButtonConfig.TwoButton,
    onDismiss: (() -> Unit)? = null
) {
    BottomSheet(visible = visible, onDismiss = { onDismiss?.invoke() }) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = Dp_260)
        ) {
            val (titleDesRef, rowRef) = createRefs()
            TitleDes(
                title = title,
                des = des,
                modifier = Modifier
                    .constrainAs(titleDesRef) {
                        top.linkTo(parent.top, margin = Dp_30)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        bottom.linkTo(rowRef.top, margin = Dp_20)
                    }
            )
            Row(
                modifier = Modifier
                    .constrainAs(rowRef) {
                        bottom.linkTo(parent.bottom, margin = Dp_30)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            ) {
                SubmitButton(
                    subTitle = buttonConfig.button1.text,
                    enable = true,
                    enableColors = buttonConfig.button1.enableColors,
                    disableColors = buttonConfig.button1.disableColors,
                    textColor = buttonConfig.button1.textColor,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_28, end = Dp_5)
                ) {
                    onDismiss?.invoke(); buttonConfig.button1.onClick?.invoke()
                }
                SubmitButton(
                    subTitle = buttonConfig.button2.text,
                    enable = true,
                    enableColors = buttonConfig.button2.enableColors,
                    disableColors = buttonConfig.button2.disableColors,
                    textColor = buttonConfig.button2.textColor,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_5, end = Dp_28)
                ) {
                    onDismiss?.invoke(); buttonConfig.button2.onClick?.invoke()
                }
            }
        }
    }
}

/**
 * 描述+底部两个按钮
 * @param des 描述
 * @param visible 弹窗是否显示
 * @param buttonConfig 传入2个ButtonParams ButtonConfig.TwoButton。
 * @param onDismiss 弹窗消失回调
 */
@Composable
fun BottomSheetDes2Button(
    des: String,
    visible: Boolean = false,
    buttonConfig: ButtonConfig.TwoButton,
    onDismiss: (() -> Unit)? = null
) {
    BottomSheet(visible = visible, onDismiss = { onDismiss?.invoke() }) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .height(Dp_202)
        ) {
            val (titleDesRef, rowRef) = createRefs()
            Text(
                text = des,
                style = TextStyle(color = ColorWhite, fontSize = Sp_16, fontWeight = FontWeight.W500),
                modifier = Modifier
                    .constrainAs(titleDesRef) {
                        top.linkTo(parent.top, margin = Dp_40)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            )
            Row(
                modifier = Modifier
                    .constrainAs(rowRef) {
                        top.linkTo(titleDesRef.bottom, margin = Dp_60)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            ) {
                SubmitButton(
                    subTitle = buttonConfig.button1.text,
                    enable = true,
                    enableColors = buttonConfig.button1.enableColors,
                    disableColors = buttonConfig.button1.disableColors,
                    textColor = buttonConfig.button1.textColor,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_28, end = Dp_5)
                ) {
                    onDismiss?.invoke(); buttonConfig.button1.onClick?.invoke()
                }
                SubmitButton(
                    subTitle = buttonConfig.button2.text,
                    enable = true,
                    enableColors = buttonConfig.button2.enableColors,
                    disableColors = buttonConfig.button2.disableColors,
                    textColor = buttonConfig.button2.textColor,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_5, end = Dp_28)
                ) {
                    onDismiss?.invoke(); buttonConfig.button2.onClick?.invoke()
                }
            }
        }
    }
}

enum class Lottie(val assetName: String) {
    Loading("loading.json"),
    Success("success.json"),
    Failed("failed.json")
}

@Composable
fun LottieAnimationLoad(
    lottie: Lottie,
    composition: LottieComposition?,
    modifier: Modifier = Modifier
) {
    when (lottie) {
        Lottie.Loading -> LottieAnimation(
            composition = composition,
            modifier = modifier.size(Dp_80),
            iterations = LottieConstants.IterateForever
        )

        Lottie.Success -> LottieAnimation(composition, modifier.size(Dp_80))
        Lottie.Failed -> LottieAnimation(composition, modifier.size(Dp_80))
    }
}

/**
 * 标题+描述+3按钮
 * @param title 标题
 * @param des 描述不传不显示（可不传）
 * @param visible 弹窗是否显示
 * @param buttonConfig 传入3个ButtonParams ButtonConfig.ThreeButton。
 * @param onDismiss 弹窗消失回调
 */
@Composable
fun BottomSheetTitleDes3Button(
    title: String,
    des: String? = null,
    visible: Boolean = false,
    buttonConfig: ButtonConfig.ThreeButton,
    onDismiss: (() -> Unit)? = null
) {
    BottomSheet(visible = visible, onDismiss = { onDismiss?.invoke() }) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            TitleDes(
                title = title,
                des = des,
                modifier = Modifier.padding(
                    start = Dp_30,
                    top = Dp_30,
                    end = Dp_30,
                    bottom = Dp_40
                )
            )
            SubmitButton(
                subTitle = buttonConfig.button1.text,
                enable = true,
                textColor = buttonConfig.button1.textColor,
                enableColors = buttonConfig.button1.enableColors,
                disableColors = buttonConfig.button1.disableColors,
                modifier = Modifier.padding(start = Dp_28, end = Dp_28, bottom = Dp_10)
            ) { onDismiss?.invoke(); buttonConfig.button1.onClick?.invoke() }
            SubmitButton(
                subTitle = buttonConfig.button2.text,
                enable = true,
                textColor = buttonConfig.button2.textColor,
                enableColors = buttonConfig.button2.enableColors,
                disableColors = buttonConfig.button2.disableColors,
                modifier = Modifier.padding(start = Dp_28, end = Dp_28, bottom = Dp_10)
            ) { onDismiss?.invoke(); buttonConfig.button2.onClick?.invoke() }
            SubmitButton(
                subTitle = buttonConfig.button3.text,
                textColor = buttonConfig.button3.textColor,
                enable = true,
                enableColors = buttonConfig.button3.enableColors,
                disableColors = buttonConfig.button3.disableColors,
                modifier = Modifier.padding(start = Dp_28, end = Dp_28, bottom = Dp_28)
            ) { onDismiss?.invoke(); buttonConfig.button3.onClick?.invoke() }
        }
    }
}

private const val SHEET_IMAGE_RATIO = 320 / 215F

/**
 * 标题+描述+X个按钮(注意按钮不要太多，demo组件)
 * @param title 标题
 * @param des 描述不传不显示（可不传）
 * @param visible 弹窗是否显示
 * @param buttonConfig 传入ButtonParams ButtonConfig.ListButton。
 * @param onDismiss 弹窗消失回调
 */
@Composable
fun BottomSheetTitleDesListButton(
    title: String,
    des: String? = null,
    visible: Boolean = false,
    buttonConfig: ButtonConfig.ListButton,
    onDismiss: (() -> Unit)? = null
) {
    BottomSheet(visible = visible, onDismiss = { onDismiss?.invoke() }) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            TitleDes(
                title = title,
                des = des,
                modifier = Modifier.padding(
                    start = Dp_30,
                    top = Dp_30,
                    end = Dp_30,
                    bottom = Dp_40
                )
            )
            buttonConfig.list.forEach {
                SubmitButton(
                    subTitle = it.text,
                    textColor = it.textColor,
                    enable = true,
                    enableColors = listOf(Color222425, Color222425),
                    disableColors = listOf(Color222425_30, Color222425_30),
                    modifier = Modifier.padding(start = Dp_28, end = Dp_28, bottom = Dp_10)
                ) { onDismiss?.invoke(); it.onClick?.invoke() }
            }
        }
    }
}

/**
 * 标题+描述+图片+底部两个按钮
 * @param title 标题
 * @param des 描述
 * @param imageId 图片id
 * @param visible 弹窗是否显示
 * @param buttonConfig 传入2个ButtonParams ButtonConfig.TwoButton。
 * @param onDismiss 弹窗消失回调
 */
@Composable
fun BottomSheetTitleDesImageButton2(
    title: String,
    des: String,
    @DrawableRes imageId: Int,
    visible: Boolean = false,
    buttonConfig: ButtonConfig.TwoButton,
    onDismiss: (() -> Unit)? = null
) {
    BottomSheet(visible = visible, onDismiss = { onDismiss?.invoke() }) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            TitleTextSp17W500(
                text = title,
                modifier = Modifier.padding(start = Dp_30, top = Dp_30, end = Dp_30, bottom = Dp_8)
            )
            TextWhite60Sp13(text = des, modifier = Modifier.padding(start = Dp_30, end = Dp_30))
            Spacer(modifier = Modifier.height(Dp_30))
            Image(
                painter = painterResource(id = imageId),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(SHEET_IMAGE_RATIO)
                    .padding(start = Dp_28, end = Dp_28)
            )
            Spacer(modifier = Modifier.height(Dp_30))
            Row(Modifier.padding(bottom = Dp_28)) {
                val enableColors = listOf(Color222425, Color222425)
                val disableColors = listOf(Color222425_30, Color222425_30)
                SubmitButton(
                    subTitle = buttonConfig.button1.text,
                    textColor = buttonConfig.button1.textColor,
                    enable = true,
                    enableColors = enableColors,
                    disableColors = disableColors,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_28, end = Dp_5)
                ) {
                    onDismiss?.invoke(); buttonConfig.button1.onClick?.invoke()
                }
                SubmitButton(
                    textColor = buttonConfig.button2.textColor,
                    subTitle = buttonConfig.button2.text,
                    enable = true,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_5, end = Dp_28)
                ) {
                    buttonConfig.button2.onClick?.invoke()
                }
            }
        }
    }
}

/**
 * 标题+单按钮+单选列表
 * @param selectItemParams 单选列表参数
 * @param visible 弹窗是否显示
 * @param checkItemSelectedBefore 选择之前的检查 检查通过才允许执行onItemSelected回调
 * @param onItemSelected 选中回调
 * @param onDismiss 弹窗消失回调
 */
@Composable
fun BottomSheetTitleSingleSelectButton(
    selectItemParams: SelectItemParams,
    visible: Boolean = false,
    checkItemSelectedBefore: (() -> Boolean) = { true },
    onItemSelected: (SelectItem) -> Unit,
    onDismiss: (() -> Unit)? = null
) {
    BottomSheet(visible = visible, onDismiss = { onDismiss?.invoke() }) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            TitleTextSp17W500(
                text = selectItemParams.title,
                modifier = Modifier.padding(start = Dp_18, top = Dp_30, end = Dp_18)
            )
            val hasDescription = selectItemParams.description != null
            if (hasDescription) {
                Spacer(modifier = Modifier.height(Dp_12))
                DescriptionText(
                    text = selectItemParams.description!!,
                    modifier = Modifier
                )
                Spacer(modifier = Modifier.height(Dp_26))
            } else {
                Spacer(modifier = Modifier.height(Dp_26))
            }
            SingleSelectList(
                items = selectItemParams.items,
                itemHeight = selectItemParams.itemHeight,
                onItemSelected = onItemSelected,
                checkItemSelectedBefore = checkItemSelectedBefore
            )
            if (hasDescription) {
                Spacer(modifier = Modifier.height(Dp_18))
            }
            SubmitButton(
                subTitle = selectItemParams.button.text,
                textColor = selectItemParams.button.textColor,
                enable = true,
                enableColors = listOf(Color222425, Color222425),
                disableColors = listOf(Color222425_30, Color222425_30),
                modifier = Modifier.padding(start = Dp_28, top = Dp_28, end = Dp_28, bottom = Dp_28)
            ) {
                onDismiss?.invoke(); selectItemParams.button.onClick?.invoke()
            }
        }
    }
}

@Suppress("MagicNumber")
@Composable
private fun SingleSelectList(
    items: List<SelectItem>,
    itemHeight: Dp,
    onItemSelected: (SelectItem) -> Unit,
    checkItemSelectedBefore: () -> Boolean
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(max = itemHeight * 5)
    ) {
        item {
            val selectedIndex = remember { mutableStateOf(items.indexOfFirst { it.isSelected }) }
            items.forEachIndexed { index, item ->
                val selected = index == selectedIndex.value
                val imageId = if (selected) R.drawable.ic_radio_selected else R.drawable.ic_radio_default
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color18191A)
                        .height(itemHeight)
                        .clickable {
                            if (selectedIndex.value == index) return@clickable
                            if (checkItemSelectedBefore()) {
                                selectedIndex.value = index
                                items.forEach { it.isSelected = false }
                                item.isSelected = true
                                onItemSelected(item)
                            }
                        },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Spacer(modifier = Modifier.width(Dp_28))
                    Column(modifier = Modifier.weight(1f)) {
                        if (item.itemDes != 0) {
                            TitleTextSp16(
                                text = if (item.name == 0) {
                                    item.nameStr
                                } else {
                                    stringResource(id = item.name)
                                },
                                modifier = Modifier
                            )
                            Spacer(modifier = Modifier.width(Dp_4))
                            DescriptionText(text = stringResource(id = item.itemDes), modifier = Modifier)
                        } else {
                            TitleTextSp16(
                                text = if (item.name == 0) {
                                    item.nameStr
                                } else {
                                    stringResource(id = item.name)
                                },
                                modifier = Modifier
                            )
                        }
                    }
                    Image(
                        painter = painterResource(id = imageId),
                        contentDescription = "selectedImage",
                        modifier = Modifier.size(Dp_22)
                    )
                    Spacer(modifier = Modifier.width(Dp_28))
                }
            }
        }
    }
}

/**
 * 标题+进度条+底部两个按钮
 * @param title 标题
 * @param des 描述不传不显示（可不传）
 * @param value 进度条进度
 * @param visible 弹窗是否显示
 * @param buttonConfig 传入2个ButtonParams ButtonConfig.TwoButton。
 * @param onDismiss 弹窗消失回调
 */
@Composable
fun BottomSheetTitleSlider2Button(
    title: String,
    des: String? = null,
    value: Float = 0F,
    visible: Boolean = false,
    buttonConfig: ButtonConfig.TwoButton,
    onSliderChange: ((Float) -> Unit)? = null,
    onDismiss: (() -> Unit)? = null
) {
    var valueState by remember { mutableStateOf(value) }
    LaunchedEffect(key1 = value, block = { valueState = value })
    BottomSheet(visible = visible, onDismiss = { onDismiss?.invoke() }) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            TitleDes(
                title = title,
                des = des,
                modifier = Modifier.padding(top = Dp_30, bottom = Dp_25)
            )
            HexaSliderWithLabel(modifier = Modifier, value = valueState, steps = 9) {
                valueState = it
                onSliderChange?.invoke(it)
            }
            Row(Modifier.padding(top = Dp_62, bottom = Dp_30)) {
                val enableColors = listOf(Color222425, Color222425)
                val disableColors = listOf(Color222425_30, Color222425_30)
                SubmitButton(
                    subTitle = buttonConfig.button1.text,
                    textColor = buttonConfig.button1.textColor,
                    enable = true,
                    enableColors = enableColors,
                    disableColors = disableColors,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_28, end = Dp_5)
                ) {
                    onDismiss?.invoke()
                    buttonConfig.button1.onClick?.invoke()
                }
                SubmitButton(
                    subTitle = buttonConfig.button2.text,
                    textColor = buttonConfig.button2.textColor,
                    enable = true,
                    enableColors = enableColors,
                    disableColors = disableColors,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_5, end = Dp_28)
                ) {
                    onDismiss?.invoke()
                    buttonConfig.button2.onClick?.invoke()
                }
            }
        }
    }
}

/**
 * 标题+单按钮+列表
 * @param selectItemParams 列表参数
 * @param visible 弹窗是否显示
 * @param onItemSelected 选中回调
 * @param onDismiss 弹窗消失回调
 */
@Composable
fun BottomSheetTitleListButton(
    selectItemParams: SelectItemParams,
    visible: Boolean = false,
    onItemSelected: (SelectItem) -> Unit,
    onDismiss: (() -> Unit)? = null
) {
    BottomSheet(visible = visible, onDismiss = { onDismiss?.invoke() }) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            TitleTextSp17W500(
                text = selectItemParams.title,
                modifier = Modifier.padding(start = Dp_18, top = Dp_30, end = Dp_18)
            )
            val hasDescription = selectItemParams.description != null
            if (hasDescription) {
                Spacer(modifier = Modifier.height(Dp_12))
                DescriptionText(
                    text = selectItemParams.description!!,
                    modifier = Modifier
                )
                Spacer(modifier = Modifier.height(Dp_30))
            } else {
                Spacer(modifier = Modifier.height(Dp_20))
            }
            SheetTitleList(
                items = selectItemParams.items,
                itemHeight = selectItemParams.itemHeight,
                onItemSelected = onItemSelected
            )
            if (hasDescription) {
                Spacer(modifier = Modifier.height(Dp_18))
            }
            SubmitButton(
                subTitle = selectItemParams.button.text,
                textColor = selectItemParams.button.textColor,
                enable = true,
                enableColors = listOf(Color222425, Color222425),
                disableColors = listOf(Color222425_30, Color222425_30),
                modifier = Modifier.padding(start = Dp_28, top = Dp_28, end = Dp_28, bottom = Dp_28)
            ) {
                onDismiss?.invoke(); selectItemParams.button.onClick?.invoke()
            }
        }
    }
}

@Composable
private fun SheetTitleList(
    items: List<SelectItem>,
    itemHeight: Dp,
    onItemSelected: (SelectItem) -> Unit
) {
    items.forEachIndexed { index, item ->
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color18191A)
                .height(itemHeight)
                .clickable {
                    onItemSelected(item)
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Spacer(modifier = Modifier.width(Dp_28))
            Column(modifier = Modifier.weight(1f)) {
                TitleTextSp16(text = stringResource(id = item.name), modifier = Modifier)
            }
            Image(
                painter = painterResource(id = R.drawable.ic_right_arrow),
                contentDescription = "selectedImage",
                modifier = Modifier.size(Dp_14)
            )
            Spacer(modifier = Modifier.width(Dp_28))
        }
    }
}

/**
 * 画中画模式兼容版本
 * 标题+描述+底部两个按钮
 * @param title 标题
 * @param des 描述不传不显示（可不传）
 * @param visible 弹窗是否显示
 * @param buttonConfig 传入2个ButtonParams ButtonConfig.TwoButton
 * 。自定义按钮，比如 取消  确认，但是用户可能定义一个 手机下载  去设置 需要在左上角增加一个取消
 * @param onDismiss 弹窗消失回调
 * @param cancel 左上角的取消按钮
 * @param onCancel 针对左上角点击操作回调
 */
@Composable
fun BottomSheetTitleDes2ButtonCompat(
    title: String? = null,
    des: String? = null,
    visible: Boolean = false,
    buttonConfig: ButtonConfig.TwoButton,
    onDismiss: (() -> Unit)? = null,
    cancel: Boolean = false,
    onCancel: (() -> Unit)? = null
) {
    BottomSheet(visible = visible, onDismiss = { onDismiss?.invoke() }) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .height(Dp_260)
        ) {
            val (cancelRef, titleDesRef, rowRef) = createRefs()
            TitleDes(
                title = title,
                des = des,
                modifier = Modifier
                    .constrainAs(titleDesRef) {
                        top.linkTo(parent.top, margin = Dp_30)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            )
            if (cancel) {
                Image(
                    painter = painterResource(R.drawable.ic_close_white),
                    contentDescription = "close",
                    modifier = Modifier
                        .constrainAs(cancelRef) {
                            top.linkTo(parent.top)
                            start.linkTo(parent.start)
                        }
                        .padding(Dp_20)
                        .size(Dp_20)
                        .clickDebounce {
                            onCancel?.invoke()
                        }
                )
            }
            Row(
                modifier = Modifier
                    .constrainAs(rowRef) {
                        bottom.linkTo(parent.bottom, margin = Dp_30)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            ) {
                SubmitButton(
                    subTitle = buttonConfig.button1.text,
                    enable = true,
                    enableColors = buttonConfig.button1.enableColors,
                    disableColors = buttonConfig.button1.disableColors,
                    textColor = buttonConfig.button1.textColor,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_28, end = Dp_5)
                ) {
                    onDismiss?.invoke(); buttonConfig.button1.onClick?.invoke()
                }
                SubmitButton(
                    subTitle = buttonConfig.button2.text,
                    enable = true,
                    enableColors = buttonConfig.button2.enableColors,
                    disableColors = buttonConfig.button2.disableColors,
                    textColor = buttonConfig.button2.textColor,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = Dp_5, end = Dp_28)
                ) {
                    onDismiss?.invoke(); buttonConfig.button2.onClick?.invoke()
                }
            }
        }
    }
}
