@file:Suppress("VariableNaming", "MaxLineLength")

package com.superhexa.supervision.library.base.webviewhelper

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.http.SslError
import android.util.Base64
import android.webkit.JavascriptInterface
import android.webkit.SslErrorHandler
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Toast
import androidx.compose.runtime.mutableStateOf
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.tencent.mmkv.MMKV
import org.json.JSONObject
import timber.log.Timber
import java.io.File
import java.io.FileInputStream

// 定义枚举类型 StreamType
enum class StreamType(val value: String) {
    SUMMARY_STREAM("summaryStream"), RECORD_STREAM("recordStream"),
    SUMMARY_SHARE_IMAGE("summaryShareImage")
}

// 定义跳转页面枚举类型 PageType
enum class PageType(val value: String) {
    RECORD_PAGE("recordPage"), // 对话记录详情页面（如口语老师，standby）
    IMAGE_PAGE("imagePage"), // 查看大图页面
    MEMORY_PAGE("memoryPage"), // 查看停车位详情界面
    MEMORY_IMAGE_PAGE("memoryImagePage") // 查看记忆图片详情界面
}

@Suppress("MagicNumber")
class WebPageHelper(private val context: Context, private val webView: WebView) {

    companion object {
        private const val TAG = "WebPageHelper"

        private const val ENV_DOMAIN = "env_domain_new"
        const val ENV_PRODUCTION: Int = 0
        const val ENV_PREVIEW: Int = 1
        const val ENV_STAGING: Int = 2
        const val ENV_PREVIEW4TEST: Int = 3

        private var fileConvertedListener: IFileConvertedListener? = null

        fun setFileConvertedListener(listener: IFileConvertedListener) {
            fileConvertedListener = listener
        }

        fun removeFileConvertedListener() {
            fileConvertedListener = null
        }

        fun getShareLink(resId: String): String {
            val env = MMKV.defaultMMKV().decodeInt(ENV_DOMAIN, 0)
            val url = when (env) {
                ENV_PREVIEW4TEST -> "https://i-preview4test.xiaomixiaoai.com/h5/ai-glasses-v2-sidekick-fe/index.html#/share?id=$resId"
                ENV_PRODUCTION -> "https://i.xiaomixiaoai.com/h5/ai-glasses-v2-sidekick-fe/index.html#/share?id=$resId"
                else -> "https://i-preview.xiaomixiaoai.com/h5/ai-glasses-v2-sidekick-fe/index.html#/share?id=$resId"
            }
            Timber.tag(TAG).i("getShareLink, url -> $url")
            return url
        }
    }

    private val lock = Any() // 线程安全锁
    private val FILE_IMAGE_SUFFIX = ".jpeg"
    private val ICON_PREFIX = "icon_"

    // 添加标志变量，表示WebView页面是否加载完成
    var isPageLoaded = mutableStateOf(false)
    val pendingRecords = mutableListOf<String>()
    fun iconPath(context: Context, imageId: String): File {
        var file = File(context.cacheDir, "$ICON_PREFIX$imageId$FILE_IMAGE_SUFFIX")
        if (!file.exists()) {
            file = File(context.cacheDir, "${ICON_PREFIX}cropped_$imageId$FILE_IMAGE_SUFFIX")
        }
        Timber.d("iconPath:$imageId,${file.exists()},${file.length()}")
        return file
    }

    @SuppressLint("SetJavaScriptEnabled")
    fun initWebView(
        isRecord: Boolean = false,
        listener: WebAppInterfaceListener,
        onPageFinished: () -> Unit?
    ) {
        // 启用 JavaScript
        val webSettings = webView.settings
        webSettings.javaScriptEnabled = true
        webSettings.allowFileAccess = true
        webSettings.domStorageEnabled = true
        webSettings.allowContentAccess = true

        WebView.setWebContentsDebuggingEnabled(true)

        // 设置 WebChromeClient（用于处理 JavaScript 弹窗等）
        webView.webChromeClient = WebChromeClient()

        //  设置 WebViewClient（控制 WebView 如何加载页面）, ignore ssl error
        class MyViewClient : WebViewClient() {
            // 页面加载完成时回调，据李博说这个不准确，所以放个空实现再次
            override fun onPageFinished(view: WebView, url: String?) {
                super.onPageFinished(view, url)
                Timber.tag(TAG).d("onPageFinished: ")
                synchronized(lock) {
                    isPageLoaded.value = true
                    flushPendingRecords()
                }
                onPageFinished?.invoke()
            }

            @SuppressLint("WebViewClientOnReceivedSslError")
            override fun onReceivedSslError(
                view: WebView?,
                handler: SslErrorHandler?,
                error: SslError?
            ) {
                Timber.tag("ssl error!!!").i(error.toString())
//                super.onReceivedSslError(view, handler, error)
                handler?.proceed() // 忽略 ssl 证书，debug 的时候用
            }
        }

        webView.webViewClient = MyViewClient()
        // 启用与 JavaScript 的交互
        webView.addJavascriptInterface(WebAppInterface(listener), "aiGlassesjsBridge")

        webSettings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        // 加载网页
        webView.loadUrl(getHost(isRecord))
    }

    private fun getHost(isRecord: Boolean): String {
        val env = MMKV.defaultMMKV().decodeInt(ENV_DOMAIN, 0)
        Timber.d("envDomain:$env")
        val url: String = if (isRecord) {
            // 区分于眼镜app旧版本, 因入参变更, 使用新url
            when (env) {
                ENV_PREVIEW4TEST -> "https://i-preview4test.xiaomixiaoai.com/h5/ai-glasses-v2-sidekick-fe/index.html#/meeting/summaryV2"
                // TODO 线上环境暂不可用，先用prev的链接
                // ENV_PRODUCTION -> "https://i.xiaomixiaoai.com/h5/ai-glasses-v2-sidekick-fe/index.html#/meeting/summaryV2"
                else -> "https://i-preview.xiaomixiaoai.com/h5/ai-glasses-v2-sidekick-fe/index.html#/meeting/summaryV2"
            }
        } else {
//            "file:///android_asset/index.html#/records"
            when (env) {
                ENV_PREVIEW4TEST -> "https://i-preview4test.xiaomixiaoai.com/h5/ai-glasses-v2-sidekick-fe/index.html#/records"
                ENV_PRODUCTION -> "https://i.xiaomixiaoai.com/h5/ai-glasses-v2-sidekick-fe/index.html#/records"
                else -> "https://i-preview.xiaomixiaoai.com/h5/ai-glasses-v2-sidekick-fe/index.html#/records"
            }
        }
        Timber.tag(TAG).d("initWebView env $env,url $url")
        return url
    }

    // TODO use vararg and recordJson
    fun recordChanged(
        streamType: StreamType,
        recordJson: String,
        timestamp: String
    ) {
        Timber.tag(TAG)
            .d("recordChanged() called with: recordJson = $recordJson, timestamp = $timestamp $isPageLoaded")
        if (!isPageLoaded.value) {
            // 如果页面未加载完成，则不执行操作
            Timber.tag(TAG).d("Page is not loaded yet, recordChanged not executed")
            return
        }

        // 创建一个 ValueCallback 用于接收 JavaScript 执行结果
        val resultCallback = ValueCallback<String> { result ->
            // 处理 JavaScript 返回的结果
            Timber.tag(TAG).d("evaluateJavascript resultCallback('$result')")
        }

        val jsonObject = wrapToJsonObj(recordJson)
        jsonObject.put("type", streamType.value)
        jsonObject.put("timestamp", timestamp)
        val descJsonStr = jsonObject.toString()

        Timber.tag(TAG)
            .d("evaluateJavascript window.onReceiveDataFromAiGlasses($descJsonStr)")

        webView.evaluateJavascript(
            "window.onReceiveDataFromAiGlasses($descJsonStr)",
            resultCallback
        )
        executeRecordChange(streamType, recordJson, timestamp)
    }

    fun requestImageOfSummary(
        streamType: StreamType,
        timestamp: String
    ) {
        Timber.tag(TAG).d(
            "requestImageOfSummary" +
                ", streamType = ${streamType.value} timestamp = $timestamp $isPageLoaded"
        )
        if (!isPageLoaded.value) {
            // 如果页面未加载完成，则不执行操作
            Timber.tag(TAG).d("Page is not loaded yet, recordChanged not executed")
            return
        }

        val jsonObject = JSONObject().apply {
            put("type", streamType.value)
            put("timestamp", timestamp)
        }.toString()

        Timber.tag(TAG).d("requestImageOfSummary, jsonObject -> $jsonObject")
        webView.post {
            webView.evaluateJavascript(
                "window.onReceiveDataFromAiGlasses($jsonObject)",
                ValueCallback { result ->
                    Timber.d("JS result: $result")
                }
            )
        }
    }

    /**
     * 执行所有缓存的记录
     */
    private fun flushPendingRecords() {
        synchronized(lock) {
            Timber.d("Flushing ${pendingRecords.size} pending records")
            val iterator = pendingRecords.iterator()
            while (iterator.hasNext()) {
                val content = iterator.next()
                executeRecordChange(
                    StreamType.SUMMARY_STREAM,
                    content,
                    System.currentTimeMillis().toString()
                )
                iterator.remove()
            }
        }
    }

    private fun wrapToJsonObj(input: String?): JSONObject {
        val jsonObject = JSONObject()
        jsonObject.put("data", input ?: "")
        return jsonObject
    }

    /**
     * 实际执行JS调用
     */
    private fun executeRecordChange(streamType: StreamType, recordJson: String, timestamp: String) {
        val jsonObject = wrapToJsonObj(recordJson).apply {
            put("type", streamType.value)
            put("timestamp", timestamp)
        }.toString()

        webView.post {
            webView.evaluateJavascript(
                "window.onReceiveDataFromAiGlasses($jsonObject)",
                ValueCallback { result ->
                    Timber.d("JS result: $result")
                }
            )
        }
    }

    @Suppress("TooGenericExceptionCaught", "MagicNumber")
    fun fileToBase64(file: File): String {
        Timber.d("fileToBase64: $file  ")
        var inputStream: FileInputStream? = null
        try {
            inputStream = FileInputStream(file)
            val buffer = ByteArray(file.length().toInt() + 100)
            val readLen = inputStream.read(buffer)
            return Base64.encodeToString(buffer, 0, readLen, Base64.DEFAULT)
        } catch (e: Exception) {
            Timber.i(TAG, "fileToBase64 exception; $e")
        } finally {
            inputStream?.close()
        }
        return ""
    }

    /**
     * @param imageBase64 Base64编码的图片
     * @return 1.是否转换成功 2.失败原因 3.转换后的Bitmap
     **/
    @Suppress("TooGenericExceptionCaught")
    fun imageBase64ToBitmap(imageBase64: String): Triple<Boolean, String?, Bitmap?> {
        return try {
            // imageBase64转Bitmap
            val base64Clean = if (imageBase64.contains("base64,")) {
                imageBase64.substringAfter("base64,")
            } else {
                imageBase64
            }
            val decodedBytes = Base64.decode(base64Clean, Base64.DEFAULT)
            val convertedBitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)

            convertedBitmap?.let {
                Triple(true, null, it)
            } ?: run {
                Triple(false, "转换为空", null)
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e("Convert to Bitmap failed, error -> $e")
            Triple(false, "转换crash", null)
        }
    }

    interface IFileConvertedListener {
        fun onConverted(result: Triple<Boolean, String?, Bitmap?>)
    }

    // 用于 JavaScript 与 Android 进行交互的接口
    inner class WebAppInterface(private val listener: WebAppInterfaceListener) {
        @JavascriptInterface
        fun getImageBase64String(jsonString: String) {
            // 返回完整的
            val jsonObj = JSONObject(jsonString)
            Timber.d("getImageBase64String: jsonObj $jsonObj")
            val onSuccess = jsonObj.getString("onSuccess") // / 获取固定callback Name
            val data = jsonObj.getJSONObject("data") // 获取其他参数
            val iconId = data.getString("iconId")
            val iconFile = iconPath(LibBaseApplication.instance, iconId)
            val base64String = fileToBase64(iconFile)
            // 强制在主线程执行 WebView 操作
            webView.post {
                // 调用 JavaScript 回调函数
                val jsCallback = "$onSuccess(`$base64String`)"

                Timber.d(TAG, "call getImageBase64String method: $jsCallback  ")
                webView.evaluateJavascript(jsCallback) { result ->
                    Timber.d(TAG, "JS callback result: $result")
                }
            }
        }

        @JavascriptInterface
        fun shareImageOfSummary(jsonString: String) {
            val jsonObj = JSONObject(jsonString)
            Timber.tag(TAG).d("shareImageOfSummary: jsonObj -> $jsonObj")
            val onSuccess = jsonObj.getString("onSuccess")
            val onFail = jsonObj.getString("onFail")
            val data = jsonObj.getJSONObject("data")
            val imageBase64 = data.optString("imageBase64")

            if (!imageBase64.isNullOrBlank()) {
                val result = imageBase64ToBitmap(imageBase64)
                if (result.first) {
                    // onSuccess
                    webView.post {
                        val jsCallback = "$onSuccess('true')"
                        webView.evaluateJavascript(jsCallback) { result ->
                            Timber.tag(TAG).d("JS callback result: $result")
                        }
                    }
                } else {
                    val reason = result.second
                    // 转换有问题，需要明确怎么处理
                }
                fileConvertedListener?.onConverted(result)
            } else {
                // onFail
                webView.post {
                    val jsCallback = "$onFail('无效imageBase64')"
                    webView.evaluateJavascript(jsCallback) { result ->
                        Timber.tag(TAG).d("JS callback result: $result")
                    }
                }
                fileConvertedListener?.onConverted(
                    Triple(false, "空imageBase64", null)
                )
            }
        }

        // 定义方法，可以被 JavaScript 调用
        @JavascriptInterface
        fun getData(jsonString: String) {
            isPageLoaded.value = true
            // 返回完整的
            val jsonObj = JSONObject(jsonString)
            val onSuccess = jsonObj.getString("onSuccess") // / 获取固定callback Name
            val onFail = jsonObj.getString("onFail") // / 获取固定失败callback Name

            // 强制在主线程执行 WebView 操作
            webView.post {
                // 处理业务逻辑（例如更新数据）
                val instructions = listener?.getAllRecords()
                // 调用 JavaScript 回调函数
                val jsonObject = wrapToJsonObj(instructions).toString()
                val jsCallback = "$onSuccess($jsonObject)"

                Timber.tag(TAG).d("call getData method: $jsCallback  ")
                webView.evaluateJavascript(jsCallback) { result ->
                    Timber.tag(TAG).d("JS callback result: $result")
                }
            }
        }

        @JavascriptInterface
        fun recJsLog(jsonString: String) {
            val jsonObj = JSONObject(jsonString)
            val onSuccess = jsonObj.getString("onSuccess") // / 获取固定callback Name
            val data = jsonObj.getJSONObject("data") // 获取其他参数
            val logInfo = data.getString("logInfo")
            Timber.tag(TAG).i("recJsLog: $logInfo")
            // 强制在主线程执行 WebView 操作
            webView.post {
                val jsCallback = "$onSuccess('true')"
                webView.evaluateJavascript(jsCallback) { result ->
                    Timber.tag(TAG).i("recJsLog callback result: $result")
                }
            }
        }

        @JavascriptInterface
        fun toast(jsonString: String) {
            Timber.tag(TAG).i("toast: $jsonString")
            val jsonObj = JSONObject(jsonString)
            val onSuccess = jsonObj.getString("onSuccess") // / 获取固定callback Name
            val onFail = jsonObj.getString("onFail") // / 获取固定失败callback Name
            val data = jsonObj.getJSONObject("data") // 获取其他参数
            val txt = data.getString("txt")
            val during = data.getInt("during")

            // 强制在主线程执行 WebView 操作
            webView.post {
                // 调用 JavaScript 回调函数
                val jsCallback = "$onSuccess('true')"
                Toast.makeText(context, txt, during).show()
                Timber.tag(TAG).d("$data call toast method: $jsCallback  ")
                webView.evaluateJavascript(jsCallback) { result ->
                    Timber.tag(TAG).d("JS callback result: $result")
                }
            }
        }

        /**
         * 如果page类型是RECORD_PAGE, 传入diaglodId即可跳转到口语老师、standby等详情页面
         * 如果page类型是IMAGE_PAGE, 是否只提供 requestId即可？
         */
        @JavascriptInterface
        fun navigateToPage(jsonString: String) {
            // 返回完整的
            val jsonObj = JSONObject(jsonString)
            val onSuccess = jsonObj.getString("onSuccess") // / 获取固定callback Name
            val data = jsonObj.getJSONObject("data") // 获取其他参数
            val pageType = data.getString("pageType")

            // {"navigateToPageParams":{"pageType":"imagePage","dialogId":"90c7aea04c04e4db8ed6f4995ab0204","imageId":"6be8d0c9c54a4c208ab27e3c6eecb8f4"}}
            // {"navigateToPageParams":{"pageType":"recordPage","transactionId":"f8f1978e21031b8651d29b7e1fc8e981","title":"英语口语老师"}}
            if (pageType == "imagePage") {
                val dialogId = data.getString("dialogId")
                val imageId = data.getString("imageId")
                listener?.navigateToImagePage(pageType, dialogId, imageId)
            } else if (pageType == "recordPage") {
                val transactionId = data.getString("transactionId")
                val title = data.getString("title")
                listener?.navigateToPage(pageType, transactionId, title)
            } else if (pageType == "memoryPage") {
                val url = data.getString("url")
                Timber.tag(TAG).d("navigateToPage url= $url")
                listener.navigateToMemoryDetail(pageType, url)
            }

            // 强制在主线程执行 WebView 操作
            webView.post {
                // 处理业务逻辑（例如更新数据）
                // 调用 JavaScript 回调函数
                val jsCallback = "$onSuccess('true')"

                Timber.d(TAG, "call navigateToPage method: $jsCallback  ")
                webView.evaluateJavascript(jsCallback) { result ->
                    Timber.d(TAG, "JS callback result: $result")
                }
            }
        }

        @JavascriptInterface
        fun removeRecord(jsonString: String) {
            isPageLoaded.value = true
            // 返回完整的
            val jsonObj = JSONObject(jsonString)
            val onSuccess = jsonObj.getString("onSuccess") // / 获取固定callback Name
            val data = jsonObj.getJSONObject("data") // 获取其他参数
            val dialogId = data.getString("dialogId")
            // 强制在主线程执行 WebView 操作
            webView.post {
                // 处理业务逻辑（例如更新数据）
                listener?.removeRecord(dialogId)
                // 调用 JavaScript 回调函数
                val jsCallback = "$onSuccess('true')"

                Timber.d(TAG, "call removeRecord method: $jsCallback  ")
                webView.evaluateJavascript(jsCallback) { result ->
                    Timber.d(TAG, "JS callback result: $result")
                }
            }
        }
    }
}

data class WebViewState(
    val webView: WebView,
    val isInitialized: Boolean
)
