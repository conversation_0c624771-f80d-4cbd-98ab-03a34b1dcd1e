package com.superhexa.supervision.filetrans.handler

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * 类描述:
 * 创建日期: 2025/3/25 on 19:02
 * 作者: qintaiyuan
 */
object MediaSpaceHandler {
    private val _mediaData = MutableStateFlow(MediaStateData())
    val mediaData: StateFlow<MediaStateData> = _mediaData
    private val mutex = Mutex()
    fun isDownloading(): Boolean {
        val transState = mediaData.value.transState
        return transState !is MediaSpaceTransState.Idle && transState !is MediaSpaceTransState.Waiting
    }

    fun isConnecting(): Boolean {
        val transState = mediaData.value.transState
        return transState is MediaSpaceTransState.Connecting
    }

    suspend fun recoverTransStateToIdle() {
        withContext(Dispatchers.IO) {
            mutex.withLock {
                val oldValue = _mediaData.value
                val newValue = oldValue.copy(transState = MediaSpaceTransState.Idle)
                _mediaData.tryEmit(newValue)
            }
        }
    }

    suspend fun dispatchAction(action: MediaSpaceAction) {
        withContext(Dispatchers.IO) {
            mutex.withLock {
                val oldValue = _mediaData.value
                Timber.d("同步MediaStateData--action=$action,oldValue=$oldValue")

                val shouldUpdate = oldValue.transState is MediaSpaceTransState.Idle ||
                    oldValue.transState is MediaSpaceTransState.Waiting
                val newValue = when (action) {
                    is MediaSpaceAction.SyncSummary -> {
                        if (shouldUpdate) {
                            oldValue.copy(
                                mediaSummary = action.summary,
                                mediaType = action.mediaType,
                                transState = if (action.summary > 0) {
                                    MediaSpaceTransState.Waiting
                                } else {
                                    MediaSpaceTransState.Idle
                                }
                            )
                        } else {
                            oldValue
                        }
                    }

                    is MediaSpaceAction.SyncSummaryAndThumb -> {
                        if (shouldUpdate) {
                            oldValue.copy(
                                mediaSummary = action.summary,
                                mediaType = action.mediaType,
                                thumbPath = action.thumbPath,
                                transState = if (action.summary > 0) {
                                    MediaSpaceTransState.Waiting
                                } else {
                                    MediaSpaceTransState.Idle
                                }
                            )
                        } else {
                            oldValue
                        }
                    }

                    is MediaSpaceAction.SyncThumb -> {
                        if (shouldUpdate) {
                            oldValue.copy(thumbPath = action.thumbPath)
                        } else {
                            oldValue
                        }
                    }

                    is MediaSpaceAction.SyncMediaTransState -> {
                        oldValue.copy(transState = action.state)
                    }

                    is MediaSpaceAction.ClearMediaData -> {
                        oldValue.copy(
                            mediaSummary = 0,
                            mediaType = SummaryType.None,
                            thumbPath = "",
                            transState = MediaSpaceTransState.Idle
                        )
                    }
                }
                _mediaData.tryEmit(newValue)
            }
        }
    }
}
