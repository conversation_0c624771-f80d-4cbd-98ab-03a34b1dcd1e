package com.xiaomi.aivs.engine.context

import android.media.AudioManager
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.superhexa.music.MusicApiService
import com.superhexa.music.data.PlayMode
import com.superhexa.music.data.PlayState
import com.superhexa.music.data.SongData
import com.superhexa.music.helper.MusicAuthHelper
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.superhexainterfaces.alipay.IAlipayModuleProxy
import com.xiaomi.ai.api.Application
import com.xiaomi.ai.api.AudioPlayer
import com.xiaomi.ai.api.Dialog
import com.xiaomi.ai.api.Execution
import com.xiaomi.ai.api.General
import com.xiaomi.ai.api.MultiModal.ImageInfo
import com.xiaomi.ai.api.MultiModal.MultiModalState
import com.xiaomi.ai.api.Settings
import com.xiaomi.ai.api.Settings.TtsConfig
import com.xiaomi.ai.api.Speaker
import com.xiaomi.ai.api.WearableController
import com.xiaomi.ai.api.common.APIUtils
import com.xiaomi.ai.api.common.Context
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.bridge.PhoneBridge
import com.xiaomi.aivs.config.ConfigCache
import com.xiaomi.aivs.data.KeyWorld
import com.xiaomi.aivs.engine.helper.CpHelper
import com.xiaomi.aivs.engine.helper.LocationHelper
import com.xiaomi.aivs.utils.AppUtils
import com.xiaomi.aivs.utils.AudioHelper
import com.xiaomi.aivs.wearable.DeviceType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import timber.log.Timber
import java.util.LinkedList

/**
 * 处理request的Context.
 */
class RequestContextHolder(private val phoneBridge: PhoneBridge) {
    private val state = Application.State()

    private fun supportContext(): Int = (
            SUPPORT_REQUEST_TYPE_CONTEXT
                    or SUPPORT_FULL_DUPLEX_CONTEXT
                    or SUPPORT_REQUEST_STATE_CONTEXT
                    or SUPPORT_SWITCH_TONE_CONTEXT
                    or SUPPORT_DEVICE_STATE_CONTEXT
                    or SUPPORT_APP_DETAILS_CONTEXT
            )

    fun createContextList(
        context: android.content.Context
    ): List<Context<*>> {
        val contexts = mutableListOf<Context<*>>()
        // 设备协同(手机小爱的Context).
        phoneBridge.contextList(context)?.let { contexts.addAll(it) }

        val supportContext = supportContext()

        // music app的详情
        if ((supportContext and SUPPORT_APP_DETAILS_CONTEXT) != 0) {
            val appContext = createAppDetailContext(context)
            appContext?.let {
                contexts.add(appContext)
            }
        }

        val playStateContext = createPlayStateContext(context)
        playStateContext?.let {
            contexts.add(playStateContext)
        }

        superXAiContext()?.let { contexts.add(it) }
        wearableSupportContext()?.let { contexts.add(it) }
        contexts.add(backPositionSwitchContext())
        devicePositionContext().let { contexts.addAll(it) }
        alipayContext()?.let {
            Timber.d("alipayContext $it")
            contexts.add(it)
        }
        Timber.d("createContextList $contexts")
        return contexts
    }

    private fun createAppDetailContext(context: android.content.Context): Context<*>? {
        val appItems = mutableListOf<Application.AppItem>()
        appItems.addAll(supportMusicAppList(context))
        return if (appItems.isNotEmpty()) {
            val appDetail = Application.AppDetailV1()
            appDetail.setAvailableApps(appItems)
            APIUtils.buildContext(appDetail)
        } else {
            null
        }
    }

    @Suppress("ComplexMethod")
    fun createPlayStateContext(context: android.content.Context): Context<*>? {
        val playbackState = AudioPlayer.PlaybackState()
        // 音量
        val volume = AudioHelper.streamVolume(context, AudioManager.STREAM_MUSIC)
        val maxVolume = AudioHelper.streamMaxVolume(
            context,
            AudioManager.STREAM_MUSIC
        )
        val realVolume = volume * PERCENT / maxVolume
        Timber.d("context Volume:$volume,$maxVolume,$realVolume")
        playbackState.setVolume(realVolume)

        // 状态.
        val isMusicActive = AudioHelper.isMusicActive(context)
        val audioPlayStatus = MusicApiService.INSTANCE.playState()
        var playStatus = if (isMusicActive || audioPlayStatus == PlayState.PLAYING) {
            PlayState.PLAYING
        } else {
            PlayState.STOP
        }
        if (audioPlayStatus == PlayState.PAUSE) {
            playStatus = PlayState.PAUSE
        }
        playbackState.setStatus(AudioPlayer.AudioPlayerStatus.values()[playStatus])

        // 添加播放内容.
        if (playStatus == PlayState.PLAYING || playStatus == PlayState.PAUSE) {
            val blePlayerInfo = getPlayerInfo()
            val metas = mutableListOf<AudioPlayer.ThirdPartyPlaybackAudioMeta>()
            val meta = AudioPlayer.ThirdPartyPlaybackAudioMeta()

            MusicApiService.INSTANCE.curSong { song ->
                song?.let {
                    if (blePlayerInfo?.title ==  it.title && blePlayerInfo.singer == it.singer) {
                        meta.setCpSoundId(removeAfterPipe(it.mid))
                        meta.setAlbum(it.albumName)
                        meta.setTitle(it.title)
                        meta.setArtist(it.singer)
                        meta.setAppName(MusicApiService.INSTANCE.curMusicCp())

                        // 模式.
                        val playMode = when (MusicApiService.INSTANCE.curPlayMode()) {
                            PlayMode.SEQUENCE -> AudioPlayer.AudioPlayerPlayMode.SEQUENCE
                            PlayMode.SINGLE -> AudioPlayer.AudioPlayerPlayMode.SINGLE
                            PlayMode.RANDOM -> AudioPlayer.AudioPlayerPlayMode.RANDOM
                            PlayMode.LIST -> AudioPlayer.AudioPlayerPlayMode.LIST
                            else -> AudioPlayer.AudioPlayerPlayMode.LIST
                        }
                        playbackState.setPlayMode(playMode)
                    } else {
                        blePlayerInfo?.let {
                            Timber.d("getPlayerInfo: $blePlayerInfo")
                            meta.setCpSoundId(removeAfterPipe(blePlayerInfo.mid))
                            meta.setAlbum(blePlayerInfo.albumName)
                            meta.setTitle(blePlayerInfo.title)
                            meta.setArtist(blePlayerInfo.singer)
                        }
                    }
                } ?: run {
                    blePlayerInfo?.let {
                        Timber.d("getPlayerInfo: $it")
                        meta.setCpSoundId(removeAfterPipe(it.mid))
                        meta.setAlbum(it.albumName)
                        meta.setTitle(it.title)
                        meta.setArtist(it.singer)
                    }
                }
            }
            metas.add(meta)
            playbackState.setThirdPartyMeta(metas)
            playbackState.setChannel(Speaker.VolumeType.MEDIA)
        }
        Timber.d("PlayStateContext isMusicActive:$isMusicActive,playbackState:$playbackState")
        return APIUtils.buildContext(AudioPlayer.PlaybackStateList(listOf(playbackState)))
    }

    private fun getPlayerInfo(): SongData? {
        return MMKVUtils.decodeString(ConstsConfig.MEDIA_PLAYER_INFO)?.let { json ->
            val type = object : TypeToken<SongData>() {}.type
            Gson().fromJson(json, type)
        }
    }

    private fun removeAfterPipe(input: String) = input.split("|").first()

    fun standbyContext(): Context<*>? {
        Timber.d("standbyContext")
        val appState = Application.AppState()
        appState.setInteractionMode(Application.InteractionMode.VOICE)

        state.setAppState(appState)
        return APIUtils.buildContext(state)
    }

    fun superXAiContext(): Context<*>? {
        val payload = Application.ApplicationStatePayload()
        payload.setSuperXiaoaiOn(true)
        state.setNextLevelState(payload)
        return APIUtils.buildContext(state)
    }

    fun ttsConfigContext(ttsConfig: TtsConfig): Context<*> {
        Timber.d("ttsConfigContext")
        val requestState = General.RequestState()
        requestState.setTtsConfig(ttsConfig)
        return APIUtils.buildContext(requestState)
    }

    private fun alipayContext(): Context<*>? {
        val privacySetting = Application.PrivacySetting()

        // 同步执行获取绑定状态
        val bindStatus = runBlocking(Dispatchers.IO) {
            IAlipayModuleProxy::class.java.impl.getBindStatus()
        }

        // 设置结果并记录日志
        privacySetting.setAliPayAuth(bindStatus)
        Timber.d("getBindStatus $bindStatus")

        state.setPrivacySetting(privacySetting)
        return APIUtils.buildContext(state)
    }

    fun continuousDialogContext(): Context<*> {
        val dialogState = Dialog.DialogState()
        dialogState.setContinuousDialog(true)
        return APIUtils.buildContext(dialogState)
    }

    fun requestStateContext(): Context<*> {
        val requestState = General.RequestState()
        requestState.setIsInitWakeup(true)
        return APIUtils.buildContext(requestState)
    }

    fun wearableSupportContext(): Context<*>? {
        val deviceState = WearableController.ControlDeviceState()
        val glass = WearableController.ControlGlassesState()
        val ability = AiSpeechEngine.INSTANCE.getGlassColorAbility()
        Timber.d("wearable ability:$ability")
        ability?.let {
            glass.setLens(
                WearableController.GlassLens().apply {
                    if (ability.first == DeviceType.SINGLE) {
                        setCurrentTransmittance(ability.second)
                        setSupportTransmittance(ability.third.toList())
                    } else {
                        setCurrentColor(ability.second)
                        setSupportColors(ability.third.toList())
                    }
                }
            )
        }
        glass.setRemainingElectricity(AiSpeechEngine.INSTANCE.getGlassElectricity())
        deviceState.setGlasses(glass)
        return APIUtils.buildContext(deviceState)
    }

    fun multiModalStateContext(params: Map<String, String>? = null): Context<*> {
        Timber.d("multiModalStateContext:$params")
        val state = MultiModalState()
        params?.get(KeyWorld.REQUEST_ID)?.let {
            val info = ImageInfo()
            info.setUploadRequestId(it)
            state.setImageInfo(info)
        }
        return APIUtils.buildContext(state)
    }

    fun devicePositionContext(): List<Context<*>> {
        val location = AiSpeechEngine.INSTANCE.curLocation()
        val contexts = mutableListOf<Context<*>>()
        Timber.i("devicePositionContext called location:$location")
        location?.let {
            val devicePositionContext = General.DevicePosition().apply {
                setLatitude(location.latitude)
                setLongitude(location.longitude)
            }
            val requestStatePayload = General.RequestState().apply {
                val clientInfo = Settings.ClientInfo().let {
                    it.setLatitude(location.latitude)
                    it.setLongitude(location.longitude)
                }
                setClientInfo(clientInfo)
            }
            contexts.add(APIUtils.buildContext(requestStatePayload))
            contexts.add(APIUtils.buildContext(devicePositionContext))
        } ?: {
            Timber.e("devicePositionContext upload devicePosition fail, location is null")
        }
        return contexts
    }

    private fun backPositionSwitchContext(): Context<*> {
        val allowBackLocation =
            LocationHelper().backLocationAllow(AiSpeechEngine.INSTANCE.appContext)
        state.setSwitchStatus(
            listOf(
                Application.SwitchStatus(
                    Application.SwitchFeature.BACK_LOCATION_AUTH,
                    allowBackLocation
                )
            )
        )
        return APIUtils.buildContext(state)
    }

    private fun supportMusicAppList(context: android.content.Context): List<Application.AppItem> {
        val sources = CpHelper.musicSources()
        val curSource = CpHelper.getMusicSource()
        Timber.d("curSource:$curSource")
        val musicAppItems = mutableListOf<Application.AppItem>()
        sources.onEach { pair ->
            pair.second.takeIf { it.isNotEmpty() }?.let { pkName ->
                val versionName = AppUtils.getVersionName(context, pkName)
                versionName?.let {
                    Timber.d("support musicApp:$pair")
                    val appItem = Application.AppItem()
                    appItem.setPkgName(pkName)
                    appItem.setVersionName(versionName)
                    appItem.setVersionCode(AppUtils.getVersionCode(context, pkName).toInt())
                    appItem.setCategory("MUSIC")
                    if (curSource == pair.first) {
                        musicAppItems.add(0, appItem)
                    } else {
                        musicAppItems.add(appItem)
                    }
                } ?: run {
                    MusicAuthHelper.setMusicCpAuth(pair.second, false)
                }
            }
        }
        return musicAppItems
    }

    /**
     * 创建禁止TTS或者NLP的Context
     *
     * @param disableTts 是否禁止TTS
     * @param disableNlp 是否禁止NLP
     */
    fun createRequestControlContext(
        disableTts: Boolean = false,
        disableNlp: Boolean = false
    ): Context<*>? {
        val disableList: MutableList<Execution.RequestControlType> = LinkedList()
        if (disableTts) {
            disableList.add(Execution.RequestControlType.TTS)
        }
        if (disableNlp) {
            disableList.add(Execution.RequestControlType.NLP)
        }

        val requestControl = Execution.RequestControl()
        requestControl.setDisabled(disableList)
        requestControl.setDomainEnv(ConfigCache.getEnvironment())

        return APIUtils.buildContext(requestControl)
    }

    companion object {

        const val SUPPORT_REQUEST_TYPE_CONTEXT: Int = 1

        const val SUPPORT_FULL_DUPLEX_CONTEXT: Int = 1 shl 1

        const val SUPPORT_APP_DETAILS_CONTEXT: Int = 1 shl 2

        const val SUPPORT_PHONE_CONTEXT: Int = 1 shl 3

        const val SUPPORT_RENEWSESSION_CONTEXT: Int = 1 shl 4

        const val SUPPORT_REQUEST_STATE_CONTEXT: Int = 1 shl 5

        const val SUPPORT_BLUETOOTH_CONTEXT: Int = 1 shl 6

        const val SUPPORT_SWITCH_TONE_CONTEXT: Int = 1 shl 7

        const val SUPPORT_TRANSLATION_CONTEXT: Int = 1 shl 8

        const val SUPPORT_DEVICE_STATE_CONTEXT: Int = 1 shl 9

        // 音量转换百分比.
        private const val PERCENT = 100
    }
}
