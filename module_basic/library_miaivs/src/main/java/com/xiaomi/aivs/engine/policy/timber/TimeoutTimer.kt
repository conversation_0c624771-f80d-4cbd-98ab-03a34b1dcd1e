package com.xiaomi.aivs.engine.policy.timber

import android.os.Handler
import android.os.HandlerThread
import com.xiaomi.aivs.data.TimeOutPolicyNode
import com.xiaomi.aivs.engine.state.EngineStateMachine
import timber.log.Timber


class TimeoutTimer(
    @TimeOutPolicyNode private val node: String,
    private val onTimeDone: () -> Unit
) : ITimeOut {
    private val handlerThread = HandlerThread("TimeoutThread").apply { start() }
    private val handler = Handler(handlerThread.looper)
    private var timeoutRunnable: TimeoutRunnable? = null

    override fun isActive(): Boolean = timeoutRunnable != null

    override fun restartTimer(reason: String?, countdownTime: Long) {
        Timber.tag(node).d("restartTimer: $reason, countdown=${countdownTime}s")
        cancelTimer(reason)
        wakeupTimer(reason, countdownTime)
    }

    @Synchronized
    private fun wakeupTimer(reason: String?, countdownTime: Long) {
        if (EngineStateMachine.isIdle()) {
            Timber.tag(node).w("非连续对话中，不启动倒计时")
            return
        }

        val delayMillis = countdownTime * TIME_UNIT
        Timber.tag(node).d("wakeupTimer: $reason, $countdownTime,delay=${delayMillis}ms")

        synchronized(this) {
            // 原子化创建任务并更新状态
            val newRunnable = TimeoutRunnable(reason) {
                Timber.tag(node).d("Timer expired: $reason")
                onTimeDone()
                synchronized(this@TimeoutTimer) { timeoutRunnable = null }
            }
            timeoutRunnable = newRunnable
            handler.postDelayed(newRunnable, delayMillis)
        }
    }

    @Synchronized
    override fun cancelTimer(reason: String?) {
        Timber.tag(node).d("cancelTimer attempt: $reason, current=${timeoutRunnable?.reason}")
        handler.removeCallbacksAndMessages(null) // 移除所有待执行任务
        timeoutRunnable = null
        Timber.tag(node).d("cancelTimer success: $reason")
    }

    // 内部Runnable类增加自检逻辑
    private inner class TimeoutRunnable(
        val reason: String?,
        private val action: () -> Unit
    ) : Runnable {
        override fun run() {
            // 仅当自己仍是当前任务时才执行
            if (timeoutRunnable !== this) {
                Timber.tag(node).d("Skip expired: $reason (overridden)")
                return
            }
            action()
        }
    }

    override fun timerNode(): String? = if (isActive()) node else null

    fun release() {
        cancelTimer("release")
        handlerThread.quitSafely() // 安全停止线程
    }

    companion object {
        private const val TIME_UNIT = 1000L
    }
}