package com.xiaomi.aivs.engine.proxy

import android.content.Context
import com.xiaomi.ai.android.core.Engine
import com.xiaomi.ai.api.common.Event
import com.xiaomi.ai.api.common.EventPayload
import com.xiaomi.ai.api.common.InstructionPayload
import com.xiaomi.aivs.data.model.AccountConfig
import com.xiaomi.aivs.data.model.AuthConfig

interface ISpeechEngineProxy {

    /**
     * 初始化.
     */
    fun init(context: Context, config: AuthConfig)

    /**
     * 启动引擎.
     * @param accountConfig 账号相关配置，鉴权使用.
     */
    fun startup(accountConfig: AccountConfig)

//    /**
//     * 若是登录, 创建引擎的时候,设置认证信息,负责启动的时候会再次调用登录接口(startUp前调用).
//     */
//    fun setAuthorizationTokens(accessToken: String, refreshToken: String, expireTime: Long)
//
//    /**
//     * 获取引擎的鉴权.
//     */
//    fun getAuthorization(): String?

    /**
     * 通用Event请求.
     */
    fun postEvent(
        payload: EventPayload,
        requestId: String? = null,
        params: Map<String, String>? = null,
        preRunnable: Runnable? = null,
        withContext: Boolean = true
    ): String

    fun postEvent(
        event: Event<*>
    )

    fun postFeedBackEvent()

    fun saveFeedBackAsr(asr: String, requestId: String)

    fun sendTaskBroadcast(requestId: String, requestType: String)

    /**
     * 图片Event相关请求.
     */
    fun postImageEvent(
        payload: EventPayload,
        requestId: String? = null,
        isFetchDeviceInfo: Boolean = false,
        params: Map<String, String>? = null
    ): String

    /**
     * 上传唤醒音频/对话音频的数据流.
     *
     * @param isFinal 是否是最后一帧数据
     */
    fun postSpeechData(bytes: ByteArray?, offset: Int, length: Int, isFinal: Boolean)

    fun uploadSuspectVoiceData(
        vendorName: String, eventId: String?,
        buffer: ByteArray?
    )
    /**
     * 上传图片数据.
     * @param requestId dialogId,发送图片前
     * @param format 图片Format,默认为PNG.
     * @param size 图片尺寸[宽,高].
     * @param chunk 分片信息[index,count].
     * @param bytes 图片数据.
     */
    fun postImageData(
        requestId: String,
        format: String,
        size: Pair<Int, Int>,
        chunk: Pair<Int, Int>,
        bytes: ByteArray?
    )

    /**
     * tts是否正在播报.
     */
    fun isTtsSpeaking(): Boolean

    fun getAiEngine(): Engine?

    /**
     * 开始播报.
     * @param text 播报内容.
     * @param params 附加配置参数.
     * 参数 Tips:
     * KeyWorld#PARAM_FONT携带 指令播放的角色，不传入为当前设置角色播放.
     */
    fun startTts(text: String, params: Map<String, String>? = null): String?

    /**
     * 停止播报.
     * @param dialogId 指定停止的TTS,否则停止全部.
     * @param isNeedAudioResume 是否需要续播长音频
     */
    fun stopTts(dialogId: String? = null, stopOptions: SpeechEngineProxyImpl.Companion.TtsStopOptions)

    /**
     * 播放本地物料话术.
     */
    fun playTipSound(resourceId: Int, complete: (() -> Unit)?)

    /**
     * 停止url的播报.
     */
    fun stopMediaPlayer(reason: String? = "")

    /**
     * 继续url的播报.
     */
    fun resumeMediaPlayer(reason: String? = "")

    /**
     * 暂停url的播报.
     */
    fun pauseMediaPlayer(reason: String? = "")

    /**
     * 是否长音频播放中.
     */
    fun isLongAudioPlaying(): Boolean?

    /**
     * 是否长音频暂停中.
     */
    fun isLongAudioPausing(): Boolean?

    /**
     * tts播放中, 丢失音频焦点情况下, 处理播控指令.
     */
    fun handleMediaControl(payload: InstructionPayload)

    /**
     * 中断引擎当前的请求.
     */
    fun interrupt(
        stopTts: Boolean = true,
        reason: String?,
        stopOptions: SpeechEngineProxyImpl.Companion.TtsStopOptions
    )

    /**
     * 释放引擎.
     */
    fun releaseEngine()

    /**
     * 销毁.
     */
    fun destroy()
}
