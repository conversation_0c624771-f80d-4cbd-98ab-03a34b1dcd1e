package com.xiaomi.aivs.player

import android.media.AudioAttributes
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioTrack
import com.superhexa.music.utils.LiteJsonUtils.toJson
import com.xiaomi.aivs.data.AudioPm
import com.xiaomi.aivs.data.model.Utterance
import com.xiaomi.aivs.track.EventTrack
import com.xiaomi.aivs.track.EventTrackKv
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.newSingleThreadContext
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean

@Suppress("TooManyFunctions")
class AudioPlayer(
    private val utteranceListener: UtteranceListener
) : AudioTrack.OnPlaybackPositionUpdateListener {
    private val workJob = Job()

    private val coroutineExceptionHandler = CoroutineExceptionHandler { _, exception ->
        Timber.tag(TAG).d("AudioPlayer Caught: $exception")
        release()
    }

    @OptIn(DelicateCoroutinesApi::class, ExperimentalCoroutinesApi::class)
    private val coroutineScope =
        CoroutineScope(newSingleThreadContext("AudioPlayer") + workJob + coroutineExceptionHandler)
    private val audioQueue: ArrayDeque<Utterance> = ArrayDeque()
    private val stopAudioQueue: ArrayDeque<String> = ArrayDeque()

    private var audioTrack: AudioTrack? = null
    private var inUtterance = AtomicBoolean(false)
    private var state: PlayState = PlayState.IDLE

    // 当前接收的回话数据，不一定是当前播放的回话.
    private var curUtterance: Utterance? = null
    private var loopTryCount = 0
    private var playbackHeadPosition: Int = 0
    private var curPlayUtterance: Utterance? = null

    private fun createAudioTrack(): AudioTrack {
        val sampleRate = AudioPm.SAMPLE_RATE
        val channelConfig = AudioFormat.CHANNEL_OUT_MONO
        val audioFormat = AudioFormat.ENCODING_PCM_16BIT
//        val bufferSize = AudioTrack.getMinBufferSize(sampleRate, channelConfig, audioFormat)
        val bufferSize = AudioPm.AUDIO_TRACK_BUFFER_DEFAULT

        return AudioTrack(
            AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .setLegacyStreamType(AudioManager.STREAM_MUSIC)
                .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                .build(),
            AudioFormat.Builder()
                .setSampleRate(sampleRate)
                .setChannelMask(channelConfig)
                .setEncoding(audioFormat)
                .build(),
            bufferSize,
            AudioTrack.MODE_STREAM,
            AudioManager.AUDIO_SESSION_ID_GENERATE
        )
    }

    fun onReceiveData(taskId: String?, data: ByteArray) {
//        Timber.tag(TAG).d("onReceiveData:$taskId,${data.size},${isPlaying()}")
        if (!isReceiveData()) return

        taskId?.let {
            if (curUtterance?.id != taskId) {
                curUtterance = Utterance(taskId)
                onAudioQueueAdd(curUtterance!!)
            }
            curUtterance?.sendPcmData(data)
        }
    }

    private fun onAudioQueueAdd(utterance: Utterance) = coroutineScope.launch {
        Timber.tag(TAG).d("onAudioQueueAdd:${utterance.id},${isPlaying()},${audioQueue.size}")
        if (isPlaying()) {
            audioQueue.addLast(utterance)
            checkAndPlayNext()
        }
    }

    fun onReceiveDataEnd(taskId: String?) {
        Timber.tag(TAG).d("onReceiveDataEnd:$taskId,${curUtterance?.id},${audioQueue.size}")
        curUtterance?.apply { isFinishReceived = true }
            ?: run { onUtteranceDone(Utterance(taskId ?: "")) }
    }

    private fun setNotificationMarkerPosition(utterance: Utterance) {
        val newFrames = playbackHeadPosition + utterance.frameSize()
        Timber.tag(TAG).d("setNotificationMarkerPosition:$playbackHeadPosition,$newFrames")
        this.playbackHeadPosition = newFrames
        audioTrack?.setNotificationMarkerPosition(newFrames)
    }

    private suspend fun checkAndPlayNext() {
        if (inUtterance.get()) {
            Timber.tag(TAG).w("it is in utterance.")
            return
        }
        curPlayUtterance = null
        Timber.tag(TAG).d("checkAndPlayNext")
        if (hasPendingMessages()) {
            val utterance = audioQueue.removeFirstOrNull()
            playUtterance(utterance)
        } else {
            Timber.tag(TAG).i("audioQueue is empty.")
        }
    }

    private suspend fun hasPendingMessages(): Boolean {
        return audioQueue.isNotEmpty()
    }

    private suspend fun playUtterance(utterance: Utterance?) {
        Timber.tag(TAG).d("playUtterance:${utterance?.id},${isPlaying()}")
        if (!isPlaying()) return

        utterance?.let {
            onUtteranceStart(utterance)
            loopAudioData(it, "playUtterance")
        }
    }

    @Suppress("ReturnCount", "ComplexMethod")
    @OptIn(ExperimentalCoroutinesApi::class)
    private suspend fun loopAudioData(utterance: Utterance, from: String) {
        Timber.tag(TAG).w("loopAudioData id ${utterance.id},$from")
        if (stopAudioQueue.contains(utterance.id)) {
            Timber.tag(TAG).w("it is be stopped.")
            onUtteranceStop(utterance)
            return
        }

        if (!isPlaying()) return

//        Timber.tag(TAG)
//            .d("loopAudioData:${utterance.id},${utterance.isFinishReceived},$loopTryCount")
        if (utterance.data.isEmpty && utterance.isFinishReceived) {
            // onUtteranceDone(utterance)
            onUtteranceWriteDone(utterance)
            return
        }
        kotlin.runCatching {
            utterance.data.tryReceive().getOrNull()?.let {
                audioTrack?.write(it, 0, it.size)
                    ?.takeIf { result -> result < 0 }
                    ?.also { result -> doAudioEventTrack(utterance.id, result) }
            } ?: run {
                loopTryCount++
                if (loopTryCount < LOOP_TRY_MAX_TIME) {
                    delay(LOOP_DELAY)
                } else {
                    Timber.tag(TAG).w("data is write TimeOut.")
                    onUtteranceDone(utterance)
                    curUtterance = null
                    return
                }
            }
            loopAudioData(utterance, "loopAudioData")
        }.getOrElse {
            Timber.tag(TAG).w("loopAudioData:${it.message}")
            onUtteranceDone(utterance)
        }
    }

    private fun doAudioEventTrack(dialogId: String, result: Int) {
        Timber.d("doAudioEventTrack:$dialogId,$result")
        if (result < 0) {
            Timber.tag(TAG).e("audioTrack wrote failed, error_code -> $result")
            // 播放出错，进入自动恢复机制
            recoveryAudioPlay(result, dialogId)
            val errorMsg = when (result) {
                AudioTrack.ERROR -> "Generic operation failure"
                AudioTrack.ERROR_BAD_VALUE -> "Invalid parameters (buffer, size, etc.)"
                AudioTrack.ERROR_INVALID_OPERATION -> "Called in wrong state (e.g. uninitialized)"
                AudioTrack.ERROR_DEAD_OBJECT -> "Audio system server died (need recreate AudioTrack)"
                else -> "Unknown error ($result)"
            }
            EventTrack.onEventTrack(
                dialogId = dialogId,
                key = EventTrackKv.STATE_TTS_STREAM_PLAY_FAILED,
                value = mapOf("error_code" to result, "error_msg" to errorMsg).toJson()
            )
        }
    }

    private fun onUtteranceWriteDone(utterance: Utterance) {
        utterance.isWriteDone = true
        val curPlaybackPosition = audioTrack?.playbackHeadPosition
        Timber.tag(TAG).w("onUtteranceWriteDone:${utterance.id},$curPlaybackPosition")
        val newFrames = playbackHeadPosition + utterance.frameSize()
        if (audioTrack?.playbackHeadPosition == newFrames) {
            onUtteranceDone(utterance)
        } else {
            setNotificationMarkerPosition(utterance)
        }
    }

    private fun onUtteranceStart(utterance: Utterance?) {
        Timber.tag(TAG).d("onUtteranceStart:${utterance?.id}")
        inUtterance.set(true)
        loopTryCount = 0
//        audioTrack?.flush()
        curPlayUtterance = utterance
        utteranceListener.onUtteranceStart(utterance?.id)
    }

    private fun onUtteranceDone(utterance: Utterance?) {
        Timber.tag(TAG).d("onUtteranceDone:${utterance?.id}")
        inUtterance.set(false)
        utteranceListener.onUtteranceDone(utterance?.id)
        coroutineScope.launch { checkAndPlayNext() }
    }

    private fun onUtteranceStop(utterance: Utterance) {
        Timber.tag(TAG).d("onUtteranceStop:${utterance.id}")
        inUtterance.set(false)
        stopAudioQueue.remove(utterance.id)
        utteranceListener.onUtteranceStop(utterance.id)
    }

    fun play() {
        coroutineScope.launch {
            if (state != PlayState.PLAYING) {
                state = PlayState.PLAYING
                <EMAIL> = 0
                Timber.tag(TAG).i("play-State: $state")
                if (audioTrack == null) {
                    audioTrack = createAudioTrack()
                }
                audioTrack?.apply {
                    setPlaybackPositionUpdateListener(this@AudioPlayer)
                    play()
                }
            }
        }
    }

    fun pause() {
        coroutineScope.launch {
            state = PlayState.PAUSE
            Timber.tag(TAG).i("pause-State: $state")
            checkStateToExecute({ audioTrack?.pause() }, "pause")
        }
    }

    fun resume() {
        coroutineScope.launch {
            checkStateToExecute({ audioTrack?.play() }, "resume")
            state = PlayState.PLAYING
            Timber.tag(TAG).i("resume-State: $state")
            inUtterance.set(false)
            checkAndPlayNext()
        }
    }

    fun stopWithId(utteranceId: String, reason: String) {
        Timber.tag(TAG).i("stopWithId: $reason, $state, $utteranceId")
        stopAudioQueue.add(utteranceId)
    }

    fun stop(reason: String) {
        Timber.tag(TAG).d("stop: call from $reason")
        coroutineScope.launch {
            inUtterance.set(false)
            stopAudioQueue.clear()
            if (isPlaying()) {
                curPlayUtterance?.let {
                    if (it.isWriteDone) {
                        onUtteranceStop(it)
                    } else {
                        stopWithId(it.id, "stop $reason")
                    }
                }
                audioTrack?.setPlaybackPositionUpdateListener(null)
            }
            state = PlayState.IDLE
            Timber.tag(TAG).i("stop-State: %s", state)
            audioTrack?.apply {
                checkStateToExecute({ stop() }, "stop")
                flush()
            }
            audioQueue.clear()
            curUtterance = null
        }
    }

    fun release() {
        coroutineScope.launch {
            audioTrack?.apply {
                checkStateToExecute({ stop() }, "release")
                <EMAIL> = PlayState.RELEASE
                Timber.tag(TAG).i("release-State: %s", state)
                release()
            }
            audioTrack = null
        }
    }

    fun isPlaying(): Boolean {
        val isPlaying = state == PlayState.PLAYING
//        Timber.tag(TAG).d("isPlaying:$isPlaying")
        return isPlaying
    }

    private fun isAudioTrackInitialized(): Boolean {
        val isAudioTrackInitialized = audioTrack?.state == AudioTrack.STATE_INITIALIZED
        Timber.tag(TAG).i("isAudioTrackInitialized-State: $isAudioTrackInitialized")
        return isAudioTrackInitialized
    }

    private fun checkStateToExecute(callback: (() -> Unit), from: String) {
        Timber.tag(TAG).i("checkStateToExecute: $from")
        if (isAudioTrackInitialized()) {
            try {
                callback.invoke()
            } catch (e: IllegalStateException) {
                Timber.tag(TAG).e("checkStateToExecute error: ${e.message}")
                // 处理异常状态，可能需要重新创建 AudioTrack
                audioTrack?.release()
                audioTrack = createAudioTrack().apply {
                    setPlaybackPositionUpdateListener(this@AudioPlayer)
                }
            }
        }
    }

    override fun onMarkerReached(track: AudioTrack?) {
        Timber.tag(TAG).d("onMarkerReached:${curPlayUtterance?.id}")
        onUtteranceDone(curPlayUtterance)
    }

    override fun onPeriodicNotification(track: AudioTrack?) {
        Timber.tag(TAG).d("onPeriodicNotification")
    }

    private fun isReceiveData(): Boolean {
        return state == PlayState.PLAYING || state == PlayState.PAUSE
    }

    /**
     * 自动恢复AudioTrack
     * @param errorCode 错误码
     * @param dialogId 对话ID
     */
    private fun recoveryAudioPlay(errorCode: Int, dialogId: String) {
        Timber.tag(TAG).i("recoveryAudioPlay，错误码：$errorCode $dialogId")
        val recoverySuccess = recreateAudioTrack()
        if (recoverySuccess) {
            Timber.tag(TAG).i("AudioTrack 恢复成功")
        } else {
            Timber.tag(TAG).e("AudioTrack 恢复失败")
        }
    }

    /**
     * 重新创建AudioTrack
     */
    @Suppress("TooGenericExceptionCaught")
    private fun recreateAudioTrack(): Boolean {
        return try {
            Timber.tag(TAG).i("recreateAudioTrack 重新创建AudioTrack")
            // 释放旧的AudioTrack
            audioTrack?.release()
            audioTrack = null
            // 重新创建AudioTrack
            audioTrack = createAudioTrack().apply {
                setPlaybackPositionUpdateListener(this@AudioPlayer)
                if (<EMAIL> == PlayState.PLAYING) {
                    play()
                }
            }
            true
        } catch (e: Exception) {
            Timber.tag(TAG).e("重新创建AudioTrack失败：${e.message}")
            false
        }
    }

    companion object {
        private const val TAG = "AudioTrackPlayer"
        private const val LOOP_DELAY = 30L

        // 数据为空,尝试次数500,最大15s
        private const val LOOP_TRY_MAX_TIME = 500
    }
}

enum class PlayState {
    IDLE,
    PLAYING,
    PAUSE,
    RELEASE
}
