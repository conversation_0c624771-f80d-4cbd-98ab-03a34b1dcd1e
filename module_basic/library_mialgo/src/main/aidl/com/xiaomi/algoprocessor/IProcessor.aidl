// IProcessor.aidl
package com.xiaomi.algoprocessor;
import com.xiaomi.algoprocessor.IProcessorCallback;

// Declare any non-default types here with import statements

interface IProcessor {
    /**
     * Demonstrates some basic types that you can use as parameters
     * and return values in AIDL.
     */
    void setDump(String dump);
    void setDebug(boolean debug);
    int processJpeg(String processorId, String dir, String outputPath, String ldcPath, String token, int watermarkType);
    int processVideo(String processorId, String dir, String outPath, String ldcPath, String token, int delayNum, int outputWidth, int outputHeight);
    void registerCallback(IProcessorCallback callback);
    void unregisterCallback(IProcessorCallback callback);
    void notifyClientQuit(String processorId, boolean keep);
    int getProcessingCount(String processorId);

    int processJpegBundle(String processorId, in Bundle bundle);
    int processVideoBundle(String processorId, in Bundle bundle);

    boolean genDefaultImage(String processorId, in Bundle bundle);

    boolean cancel(String processorId, String token);

    boolean sendMessage(int what, in Bundle args);
}