#include "arc_glass_capture.h"
#define LOG_TAG "ARC_GLASS_CAPTURE"

#define EV_STEP         6
#define FACE_LUMA       0

#define DUMP_DISABLE    0
#define DUMP_ENABLE     1
#define VERSION   "20250804_2026"
MUInt64 ProcessCallback(M<PERSON><PERSON> lProgress, MLong lStatus, MVoid *pParam)
{
    ARC_LOGD("[%s]: ProcessCallback lProgress=%ld, lStatus=%ld, pParam=%p\n", LOG_TAG, lProgress, lStatus, pParam);
    return 0;
}

ArcGlassCapture::ArcGlassCapture()
{
    printf("ArcGlassCapture\n");
    //hard code for metaData
    mMetaData[0].fFaceLuma                 = FACE_LUMA;

    //hard code for initParam
    mInitParam.i32ObjSize                  = sizeof(ARC_GVCI_INITPARAM);
    mInitParam.pipelineControl.bEnablePDC  = true;
    mInitParam.pipelineControl.bEnableFRT  = true;
    mInitParam.pipelineControl.bEnableFringeRemove = true;
    mInitParam.i32CameraType               = ARC_GVC_CAMERA_REAR;
    mInitParam.i32EvStep                   = EV_STEP;

    //hard code for others
    mFormat    = ASVL_PAF_NV21;
    mRefIndex  = 0;
}

ArcGlassCapture::~ArcGlassCapture()
{
    printf("~ArcGlassCapture\n");
    if (NULL != mCaliData.pCaliData)
    {
        free(mCaliData.pCaliData);
        mCaliData.pCaliData = NULL;
    }
}

MUInt64 ArcGlassCapture::init(CaptureInitParam* param)
{
    ARC_LOGD("ArcGlassVideo_VERSION: %s", VERSION);
    MUInt64 nRet    = MOK;
    ARC_LOGD("[%s]: init logLevel = %d, bypass = %d, dump = %d, enableSLC = %d, metaPath =%s\n", LOG_TAG, param->loglevel, param->bypass, param->needDump, param->enableSLC, param->metaPath[0]);
    ARC_GVCI_SetLogLevel(param->loglevel > 0 ? ARC_GVC_LOGLEVEL_2 : ARC_GVC_LOGLEVEL_0, NULL);
    ARC_LOGD("[%s]: init version =%s, status =%llx\n", LOG_TAG, ARC_GVCI_GetVersion()->Version, ARC_GVCI_GetVersionStatus());

    if (NULL != param->dumpPath)
    {
        memcpy(m_dumpPath, param->dumpPath, sizeof(m_dumpPath));
        ARC_LOGD("[%s]: init dumpPath =%s\n", LOG_TAG, m_dumpPath);
    }

    int pipe                          = -1;
    mMetaData[0].rtCropRegion.left    = 0;
    mMetaData[0].rtCropRegion.top     = 0;
    mMetaData[0].rtCropRegion.right   = param->maxWidth;
    mMetaData[0].rtCropRegion.bottom  = param->maxHeight;
    
    for (int i = 0; i < param->metaNum; i++)
    {
        ARC_LOGE("[%s]: meta[%d]:%s\n", LOG_TAG, i, param->metaPath[i]);
    }

    nRet = readMetaData(param->metaNum > 1 ? param->metaPath[param->metaNum-1] : param->metaPath[0], &mFrameCount, &mMetaData[0], &mFaceInfo, &mInitParam, &pipe);
    if (nRet != MOK)
    {
        ARC_LOGE("[%s]: init error, readMetaData ret =%d\n", LOG_TAG, nRet);
        return nRet;
    }

    mInitParam.i32MaxWidth   = param->maxWidth;
    mInitParam.i32MaxHeight  = param->maxHeight;
    mInitParam.i32HDRMode    = getCameraType(pipe, mFrameCount, &mMetaData[0]);

    //add for SLC
    mInitParam.pipelineControl.bEnableSLC = (MInt32)param->enableSLC;
    MFloat GravityExtrinsicMatrix[16] = {   
                                            0, 1, 0, 0, 
                                            1, 0, 0, 0, 
                                            0, 0, 1, 0, 
                                            0, 0, 0, 1
                                        };
    memcpy(mInitParam.fGravityExtrinsicMatrix, GravityExtrinsicMatrix, sizeof(GravityExtrinsicMatrix));

    mRefIndex = getRefIndex(pipe, mFrameCount, &mMetaData[0]);
    mInitParam.i32LLShotCaptureMode = mFrameCount < 4 ?
            ARC_GVCI_LLSHOT_CAPTURE_MODE_MOVE : ARC_GVCI_LLSHOT_CAPTURE_MODE_DEFAULT;

    ARC_LOGE("[%s]: pipeline=%d fMaxFov=%f\n", LOG_TAG, pipe, mInitParam.fMaxFov);

    nRet = setupPipeline(pipe, &mInitParam);
    if (nRet != MOK)
    {
        ARC_LOGE("[%s]: init error, setupPipeline %d ret =%d\n", LOG_TAG, pipe, nRet);
        return nRet;
    }

    //SN暂时特殊处理
    if (ARC_GVCI_IMAGE_PIPELINE_SUPERNIGHT == pipe) 
    {
        mMetaData[mRefIndex] = mMetaData[0];
        mMetaData[mRefIndex].fEV = 0.0;
    }

    nRet = readCaliData(param->LDC_binPath, &mCaliData);
    if (nRet != MOK)
    {
        ARC_LOGE("[%s]: init error, readCaliData ret =%d\n", LOG_TAG, nRet);
        return nRet;
    }

    bool bEnableBypass = param->bypass;
    nRet = ARC_GVCI_SetByPass(bEnableBypass);
    ARC_LOGD("[%s]: ARC_GVCI_SetByPass bEnableBypass =%d\n", LOG_TAG, bEnableBypass);
    if (nRet != MOK)
    {
        ARC_LOGE("[%s]: init error, ARC_GVCI_SetByPass ret =%d\n", LOG_TAG, nRet);
        return nRet;
    }

    MInt32 needDump = param->needDump ? DUMP_ENABLE : DUMP_DISABLE;
    nRet = ARC_GVCI_SetDump(needDump, m_dumpPath);
    ARC_LOGD("[%s]: ARC_GVCI_SetDump nRet=%d, needDump = %d, dumpPath=%s\n", LOG_TAG, nRet, needDump, m_dumpPath);
    if (nRet != MOK)
    {
        ARC_LOGE("[%s]: init error, ARC_GVCI_SetDump ret =%d\n", LOG_TAG, nRet);
        return nRet;
    }

    nRet = ARC_GVCI_Init(&mHandle, &mInitParam);
    ARC_LOGD("[%s]: ARC_GVCI_Init ret =%d\n", LOG_TAG, nRet);
    return nRet;
}
    
MUInt64 ArcGlassCapture::process(CaptureProcessInput* input, CaptureProcessOutput* output)
{
    MUInt64 nRet        = MOK;
    int framecount      = input->frameNum;
    int refIndex        = mRefIndex;
    int format          = mFormat; 

    if (NULL == mHandle) 
    {
        ARC_LOGE("[%s]: process error, algo handle is null\n", LOG_TAG);
        return ERROR_BAD_STATUS;
    }

    if (input->frameNum != mFrameCount)
    {
        ARC_LOGE("[%s]: process error, frameNum[%d] not match meta.inputFrameNum[%d] \n", LOG_TAG, input->frameNum, mFrameCount);
        return ERROR_INPUT_PARAM;
    }

    nRet = ARC_GVCI_SetDCCaliData(mHandle, &mCaliData);
    ARC_LOGD("[%s]: ARC_PDC_SetDCCaliData ret =%d\n", LOG_TAG, nRet);

	ARC_GVCI_SetCallback(mHandle, ProcessCallback, NULL);
	ARC_LOGD("[%s]: ARC_GVCI_SetCallback\n", LOG_TAG);

	mSrcInputs.i32ImgNum 	    = framecount;
	mSrcInputs.i32RefIndex	    = refIndex;
	mSrcInputs.pFaceInfo[0]     = &mFaceInfo;

	for (int i=0; i<framecount; i++) 
	{
        mSrcInputs.pMetadata[i] = &mMetaData[i];
        mSrcInputs.pImages[i] 	= &mSrcImage[i];
        mSrcInputs.pImagesFd[i] = NULL;
        mSrcInputs.pFaceInfo[i] = &mFaceInfo;
	}
	prepareInputOffScreen(format, framecount, input, &mSrcImage[0]);
    

    for (int i=0; i<framecount; i++)
    {
        ARC_LOGD("[%s]: pMetadata[%d]=%p, pImages[%d].ppu8Plane[0]=%p, mSrcInputs.pImagesFd[%d]=%p, mSrcInputs.pFaceInfo[%d]=%p\n", 
            LOG_TAG, 
            i, mSrcInputs.pMetadata[i], 
            i, mSrcInputs.pImages[i]->ppu8Plane[0], 
            i, mSrcInputs.pImagesFd[i], 
            i, mSrcInputs.pFaceInfo[i]);
    }

    mOutputInfo.i32ObjSize = sizeof(ARC_GVCI_OUTPUTINFO);
    mOutputInfo.i32ImgNum = mInitParam.pipelineControl.bEnableSLC > 0 ? 2 : 1;
    mOutputInfo.pImages[0] = &mDstImage[0]; 
    if (mInitParam.pipelineControl.bEnableSLC) mOutputInfo.pImages[1] = &mDstImage[1];
    for (int i = 0; i < mOutputInfo.i32ImgNum; i++) mOutputInfo.pImages[i] = &mDstImage[i];
       
    prepareOutputOffScreen(format, mOutputInfo.i32ImgNum, output, &mDstImage[0]);
    
	nRet = ARC_GVCI_Process(mHandle, &mSrcInputs, &mOutputInfo);

    output->ImageTypes[0] = mOutputInfo.eImageTypes[0];
    output->ImageTypes[1] = mOutputInfo.eImageTypes[1];
    ARC_LOGD("[%s]: mOutputInfo eImageTypes[0] =%d, eImageTypes[1] =%d\n", LOG_TAG, output->ImageTypes[0], output->ImageTypes[1]);
	ARC_LOGD("[%s]: ARC_GVCI_Process ret =%d\n", LOG_TAG, nRet);
    return nRet;
}
    
MUInt64 ArcGlassCapture::setParam(CaptureProcessParam* param)
{
    if (NULL == mHandle) 
    {
        ARC_LOGE("[%s]: setParam error, algo handle is null\n", LOG_TAG);
        return ERROR_BAD_STATUS;
    }

    return MOK;
}
    
MUInt64 ArcGlassCapture::uninit()
{
    if (NULL == mHandle) 
    {
        ARC_LOGE("[%s]: uninit error, algo handle is null\n", LOG_TAG);
        return ERROR_BAD_STATUS;
    }

    MUInt64 nRet = ARC_GVCI_Uninit(&mHandle);
	ARC_LOGD("[%s]: ARC_GVCI_Uninit ret =%d\n", LOG_TAG, nRet);
    return nRet;
}


MUInt64 ArcGlassCapture::readMetaData(char* metaFilePath, MInt32* frameCount, LPARC_GVCI_METADATA pMetaData, LPARC_GVC_FACEINFO pFaceInfo, ARC_GVCI_INITPARAM *params, int *pipeline)
{
    ARC_LOGD("[%s]: readMetaData metaPath = %s\n", LOG_TAG, metaFilePath);
    MFloat fShutter                     = 0; //unit(s)
    MInt32 i32InputFrameNum             = 0;
    MFloat evList[MAX_INPUT_IMAGE_NUM]  = {0};
    pMetaData->i32ObjSize               = sizeof(ARC_GVCI_METADATA);
    pFaceInfo->i32ObjSize               = sizeof(ARC_GVC_FACEINFO);

    FILE *pf = fopen(metaFilePath, "r");
	if (pf)
	{
        char buff[256];
        char subStr[256];
		while (!feof(pf))
        {
			memset(buff, 0, sizeof(buff));
			fgets(buff, sizeof(buff)-1, pf);
            
            if (strstr(buff, "pipelineType"))
            {
				sscanf(buff, "pipelineType:%d", pipeline);
            } 
 			else if (strstr(buff, "iso"))
            {
				sscanf(buff, "iso:%d", &pMetaData->i32ISOValue);
            } 
            else if (strstr(buff, "DeviceOrient"))
            {
				sscanf(buff, "DeviceOrient:%d", &pMetaData->i32DeviceRoll);
            }
            else if (strstr(buff, "sensorGain"))
            {
				sscanf(buff, "sensorGain:%f", &pMetaData->fSensorGain);
            }
            else if (strstr(buff, "ispGain"))
            {
				sscanf(buff, "ispGain:%f", &pMetaData->fISPGain);
            }
            else if (strstr(buff, "totalGain"))
            {
				sscanf(buff, "totalGain:%f", &pMetaData->fTotalGain);
            }
            else if (strstr(buff, "luxIndex"))
            {
				sscanf(buff, "luxIndex:%f", &pMetaData->fLuxIndex);
            }
            else if (strstr(buff, "adrcGain"))
            {
				sscanf(buff, "adrcGain:%f", &pMetaData->fAdrcGain);
            }
            else if (strstr(buff, "shutter"))
            {
                MLong value = 0;
				sscanf(buff, "shutter:%ld", &value);
                fShutter = 1.0 * value / 1000000000;
                ARC_LOGD("[%s]: readMetaData fShutter = %f\n", LOG_TAG, fShutter);
            }
            else if (strstr(buff, "inputFrameNum"))
            {
				sscanf(buff, "inputFrameNum:%d", &i32InputFrameNum);
            }
            else if (strstr(buff, "evvalue"))
            {
                memset(subStr, 0, sizeof(subStr));
                sscanf(buff, "evvalue:%s", subStr);
                char *temp = strtok(subStr,",");
                int index = 0;
				while(temp)
				{
                    evList[index++] = atof(temp);
                    temp = strtok(NULL,",");
                }
                for (int i=0; i<index; i++) {
                    ARC_LOGD("[%s]: readMetaData evvalue[%d] = %f\n", LOG_TAG, i, evList[i]);
                }
            }
            else if (strstr(buff, "fMaxFov"))
            {
				sscanf(buff, "fMaxFov:%f", &params->fMaxFov);
                ARC_LOGD("[%s]: readMetaData fMaxFov = %f\n", LOG_TAG, params->fMaxFov);
            }
            else if (strstr(buff, "facenum:")) 
			{
				sscanf(buff, "facenum:%d", &pFaceInfo->i32FaceNum);
                if (pFaceInfo->i32FaceNum > ARC_GVC_MAX_FACE_NUM) pFaceInfo->i32FaceNum = ARC_GVC_MAX_FACE_NUM;
                ARC_LOGD("[%s]: readMetaData facenum = %d\n", LOG_TAG, pFaceInfo->i32FaceNum);
			}
            else if (strstr(buff, "face:"))
            {
                memset(subStr, 0, sizeof(subStr));
                sscanf(buff, "face:%s", subStr);
                char *temp = strtok(subStr,",");
				int curCount = 0;
                int group = 0;
                int index = 0;
				while(temp)
				{
                    group = curCount / 5;
                    index = curCount % 5;
                    if (group < ARC_GVC_MAX_FACE_NUM)
					{
                        if (index == 0) 
                        {
                            pFaceInfo->rcFace[group].left = atoi(temp);
                        }
                        else if (index == 1)
                        {
                            pFaceInfo->rcFace[group].top = atoi(temp);
                        }                    
                        else if (index == 2)
                        {
                            pFaceInfo->rcFace[group].right = atoi(temp);
                        }
                        else if (index == 3)
                        {
                            pFaceInfo->rcFace[group].bottom = atoi(temp);
                        }                    
                        else if (index == 4)
                        {
                            pFaceInfo->i32FaceOrient[group] = atoi(temp);
                            ARC_LOGD("[%s]: readMetaData faceRect[%d][%d,%d,%d,%d] orient= %d\n", LOG_TAG, group, 
                                    pFaceInfo->rcFace[group].left,
                                    pFaceInfo->rcFace[group].top,
                                    pFaceInfo->rcFace[group].right,
                                    pFaceInfo->rcFace[group].bottom,
                                    pFaceInfo->i32FaceOrient[group]);
                        }
                    }
					temp = strtok(NULL,",");
                    curCount ++;
				}
            }
            else if (strstr(buff, "gravity:"))
            {
                memset(subStr, 0, sizeof(subStr));
                sscanf(buff, "gravity:%s", subStr);
                char *temp = strtok(subStr, ",");
                long long frameTimestamp = atoll(temp); // 获取帧时间戳
                temp = strtok(NULL, ",");
                long long gravityTimestamp = atoll(temp); // 获取gravity时间戳
                temp = strtok(NULL,",");
                int index = 0;
				while(temp)
				{
                    pMetaData->fGravityData[index++] = atof(temp);
                    temp = strtok(NULL,",");
                }
                ARC_LOGD("[%s]: readMetaData frameTimestamp = %ld, gravityTimestamp = %ld, gravity_x = %f, gravity_y = %f, gravity_z = %f\n", 
                        LOG_TAG, frameTimestamp, gravityTimestamp, 
                        pMetaData->fGravityData[0], 
                        pMetaData->fGravityData[1], 
                        pMetaData->fGravityData[2]);
            }

		}
		fclose(pf);
        
        *frameCount                     = i32InputFrameNum;
        pMetaData->i32ImageRoll         = pMetaData->i32DeviceRoll;
        pMetaData->fExposureValue       = fShutter * pMetaData->i32ISOValue;
        for(int i=0; i<i32InputFrameNum; i++)
        {
            if (i < MAX_INPUT_IMAGE_NUM)
            {
                pMetaData[i].i32ObjSize = sizeof(ARC_GVCI_METADATA);
                pMetaData[i].fEV = evList[i];

                ARC_LOGD("[%s]: readMetaData frameCount=%d index[%d]: pipeline=%d, iso=%d, deviceRoll=%d, imageRoll=%d, sensorGain=%f, ispGain=%f, totalGain=%f, luxIndex=%f, adrcGain=%f, faceLuma=%f, exposure=%f, ev=%f, cropRegin=[%d,%d,%d,%d]\n",
                    LOG_TAG,
                    i32InputFrameNum, i,
                    *pipeline,
                    pMetaData[i].i32ISOValue, 
                    pMetaData[i].i32DeviceRoll,
                    pMetaData[i].i32ImageRoll,
                    pMetaData[i].fSensorGain,
                    pMetaData[i].fISPGain,
                    pMetaData[i].fTotalGain,
                    pMetaData[i].fLuxIndex,
                    pMetaData[i].fAdrcGain,
                    pMetaData[i].fFaceLuma,
                    pMetaData[i].fExposureValue,
                    pMetaData[i].fEV,
                    pMetaData[i].rtCropRegion.left,
                    pMetaData[i].rtCropRegion.top,
                    pMetaData[i].rtCropRegion.right,
                    pMetaData[i].rtCropRegion.bottom);
            }
        }
        return MOK;
    }
    else
    {
        ARC_LOGE("[%s]: readMetaData open filepath failed %s\n", LOG_TAG, metaFilePath);
        return ERROR_BAD_STATUS;
    }
}

MUInt64 ArcGlassCapture::setupPipeline(MInt32 pipeline, LPARC_GVCI_INITPARAM  pInitParam)
{
    MUInt64 nRet = MOK;
    switch (pipeline)
    {
    case GLASS_IMAGE_PIPELINE_HDR:
        pInitParam->i32Pipeline = ARC_GVCI_IMAGE_PIPELINE_HDR;
        break;
    case GLASS_IMAGE_PIPELINE_LLHDR:
        pInitParam->i32Pipeline = ARC_GVCI_IMAGE_PIPELINE_LLHDR;
        break; 
    case GLASS_IMAGE_PIPELINE_LLS:
        pInitParam->i32Pipeline = ARC_GVCI_IMAGE_PIPELINE_LLS;
        break;  
    case GLASS_IMAGE_PIPELINE_SUPERNIGHT:
        pInitParam->i32Pipeline = ARC_GVCI_IMAGE_PIPELINE_SUPERNIGHT;
        break;
    case GLASS_IMAGE_PIPELINE_SINGLEFRAME:
        pInitParam->i32Pipeline = ARC_GVCI_IMAGE_PIPELINE_SINGLEFRAME;
        break;                 
    default:
        nRet = ERROR_BAD_STATUS;
        break;
    }

    ARC_LOGD("[%s]: setupPipeline ret =%d, pipeline =%d->%d\n", LOG_TAG, nRet, pipeline, pInitParam->i32Pipeline);
    return nRet;
}

MInt32 ArcGlassCapture::getCameraType(MInt32 pipeline, int frameCount, LPARC_GVCI_METADATA pMetaData)
{
    // only uesed under PIPELINE_HDR
    MInt32 hdrMode = ARC_GVCI_HDR_MODE_0;
    if (pipeline == GLASS_IMAGE_PIPELINE_HDR && frameCount == ARC_GVCI_HDR_MAX_INPUT_IMAGE_NUM)
    {
        ARC_LOGD("[%s]: getCameraType EVList[%f,%f,%f]\n", LOG_TAG, pMetaData[0].fEV, pMetaData[1].fEV, pMetaData[2].fEV);
        if (pMetaData[1].fEV < 0 && pMetaData[2].fEV < 0)
        {
            hdrMode = ARC_GVCI_HDR_MODE_2;
        }
    }
    ARC_LOGD("[%s]: getCameraType pipeline=%d, frameCount=%d -> CameraType=%d\n", LOG_TAG, pipeline, frameCount, hdrMode);
    return hdrMode;
}

MInt32 ArcGlassCapture::getRefIndex(MInt32 pipeline, int frameCount, LPARC_GVCI_METADATA pMetaData)
{
    // only uesed under PIPELINE_SUPERNIGHT
    int maxEV = pMetaData[0].fEV;
    int refIndex = 0;
    if (pipeline == GLASS_IMAGE_PIPELINE_SUPERNIGHT)
    {
        ARC_LOGD("[%s]: getRefIndex EVList[%f,%f,%f]\n", LOG_TAG, pMetaData[0].fEV, pMetaData[1].fEV, pMetaData[2].fEV);
        for (int i = 0; i < frameCount; i++)
        {
            if (maxEV < pMetaData[i].fEV)
            {
                maxEV = pMetaData[i].fEV;
                refIndex = i;
            }
        }
    }
    ARC_LOGD("[%s]: getRefIndex pipeline=%d, frameCount=%d -> RefIndex=%d\n", LOG_TAG, pipeline, frameCount, refIndex);
    return refIndex;
}

MUInt64 ArcGlassCapture::readCaliData(char* binPath, LPARC_GVC_CALIDATA_I pCaliData)
{
    pCaliData->i32ObjSize = sizeof(ARC_GVC_CALIDATA_I);
	FILE *pf = fopen(binPath, "rb");
	if (pf)
	{
        fseek(pf, 0, SEEK_END);
		long fileSize = ftell(pf);
        pCaliData->i32CaliDataLen = fileSize;
        pCaliData->pCaliData = (MByte*)malloc(fileSize);

        rewind(pf);
        fread(pCaliData->pCaliData, fileSize, 1, pf);
        fclose(pf);
        ARC_LOGD("[%s]: readCaliData success size=%d, %s\n", LOG_TAG, pCaliData->i32CaliDataLen, binPath);
        return MOK;
	}
	else
	{
        ARC_LOGE("[%s]: readCaliData failed %s\n", LOG_TAG, binPath);
        return ERROR_BAD_STATUS;
	}
}

void ArcGlassCapture::prepareInputOffScreen(int format, int framecount, CaptureProcessInput* input, LPASVLOFFSCREEN  pSrcImage)
{
    int width  = input->width;
    int height = input->height;
    int pitch  = input->width;

	for(int i=0; i<framecount; i++)
	{
		LPASVLOFFSCREEN pCurSrcImage = pSrcImage + i;
		void *pBuffer = input->buffer[i];
		pCurSrcImage->u32PixelArrayFormat = format;
		pCurSrcImage->ppu8Plane[0] = static_cast<MUInt8 *>(pBuffer);
		pCurSrcImage->ppu8Plane[1] = static_cast<MUInt8 *>(pBuffer) + pitch * height;
		pCurSrcImage->pi32Pitch[0] = pitch;
		pCurSrcImage->pi32Pitch[1] = pitch;
		pCurSrcImage->i32Width     = width;
		pCurSrcImage->i32Height    = height;

		ARC_LOGD("[%s]: INPUT buffer[%d]=%p \n", LOG_TAG, i, pCurSrcImage->ppu8Plane[0]);
	}
}
	

void ArcGlassCapture::prepareOutputOffScreen(int format, int framecount, CaptureProcessOutput* output,  LPASVLOFFSCREEN  pDstImage)
{
    int width  = output->width;
    int height = output->height;
    int pitch  = output->width;
    
    for (int i = 0; i < framecount; i++)
    {
        LPASVLOFFSCREEN pCurDstImage = pDstImage + i;
        void *pBuffer = output->buffer[i];
        pCurDstImage->u32PixelArrayFormat = format;
        pCurDstImage->ppu8Plane[0] = static_cast<MUInt8 *>(pBuffer);
        pCurDstImage->ppu8Plane[1] = static_cast<MUInt8 *>(pBuffer) + pitch * height;
        pCurDstImage->pi32Pitch[0] = pitch;
        pCurDstImage->pi32Pitch[1] = pitch;
        pCurDstImage->i32Width     = width;
        pCurDstImage->i32Height    = height;
        ARC_LOGD("[%s]: Normal OUTPUT buffer=%p \n", LOG_TAG, pCurDstImage->ppu8Plane[0]);
    }
}