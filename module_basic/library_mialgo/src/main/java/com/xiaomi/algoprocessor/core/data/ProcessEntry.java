package com.xiaomi.algoprocessor.core.data;

import androidx.annotation.GuardedBy;

import java.util.List;

public class ProcessEntry {
    public static final int INPUT_FORMAT_NV21 = 1;
    public static final int INPUT_FORMAT_HEIC = 2;

    public static final int PROCESS_STATUS_DECODING = 1;
    public static final int PROCESS_STATUS_ENCODING = 2;
    public String processorId;
    public final String token;
    /**
     * 视频处理的话： 一个MP4文件， 会对应若干个meta文件和 若干个gyro文件，
     * 我们需要在 init 的时候把所有的 meta 和 gyro文件路径传给算法。
     * 每处理一个视频文件，都需要init一次
     * <p>
     * 对于照片jpeg处理的话: 若干个NV21文件， 会对应若干个 meta 文件和 若干个gyro文件，
     * 我们要在init的时候只传入一个 ev0的 meta文件， 和所有的gyro文件。
     * 每处理一个jpeg图片，都需要init一次
     * <p>
     * 处理视频， inputPath代表MP4的路径
     * 处理照片， inputPath就是照片文件夹的路径
     */
    public String inputPath;
    public String outputPath;

    public int metaNum;
    public List<String> metaPath;

    public int gyroNum;
    public List<String> gyroPath;

    public String ldcPath;

    //视频想要裁剪的之后的百分比，一般都写成80吧
    public int cropW;
    public int cropH;

    public int blurStrength;

    public int width;
    public int height;
    public int delayNum;

    @GuardedBy("lock")
    public long handle; //在init的时候由JNI填充

    public String debugBinPath;

    public String dumpPath;

    public String exifPath;

    public int outputWidth;
    public int outputHeight;

    public String iccPath;

    public boolean enableAlgoDebug;

    /**
     * 取值范围 WatermarkUtil.WATERMARK_TYPE_NONE 和 WatermarkUtil.WATERMARK_TYPE_SINGLE_LINE
     */
    public int watermarkType;

    /**
     * 取值范围 ProcessEntry.INPUT_FORMAT_NV21 和 ProcessEntry.INPUT_FORMAT_HEIC
     */
    public int inputFormatType; //记录原始的图片格式，兼容NV21做输入 和 HEIC做输入

    /**
     * 取值范围 ProcessParams.OUTPUT_IMAGE_TYPE_JPEG 和 ProcessParams.OUTPUT_IMAGE_TYPE_HEIC
     */
    public int outputImageType;//输出图片格式， 默认是jpeg，也可以设置成heic
    public String unrevisedPath;//没有经过修正的输出图片路径

    public boolean decoderStopped;//解码器是否被强行停止
    public int decoderColorFormat;//解码器配置的解码格式，现在一般都是SemiPlanar
    public int decoderOutputFormat;//解码器实际的输出格式

    public final Object lock = new Object();
    @GuardedBy("lock")
    public boolean cancel;//处理流程是否被强制取消

    /**
     * PROCESS_STATUS_DECODING 和 PROCESS_STATUS_ENCODING
     */
    @GuardedBy("lock")
    public int processStatus;//处理流程的状态

    //目前只用于调试目的
    public int lastAlgoNum = -1;
    //目前只用于调试目的
    public int frameRate;

    public volatile long eosPresentationTime = -1L; // codec base

    public int extraResultCode;

    public int errorCount = 0;

    public ProcessEntry(String token) {
        this.token = token;
    }
}