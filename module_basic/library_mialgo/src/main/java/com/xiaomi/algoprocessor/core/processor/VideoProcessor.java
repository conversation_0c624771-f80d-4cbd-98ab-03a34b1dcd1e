package com.xiaomi.algoprocessor.core.processor;

import static com.xiaomi.algoprocessor.core.processor.ProcessParams.TOKEN;
import static com.xiaomi.algoprocessor.core.processor.server.BaseProcessorServer.PROCESSOR_TYPE_VIDEO;
import static com.xiaomi.algoprocessor.core.processor.server.BaseProcessorServer.PROCESS_RESULT_NULL_CLIENT_PROCESSOR;

import android.content.Context;
import android.os.Bundle;
import android.os.RemoteException;

import androidx.annotation.NonNull;

import com.xiaomi.algoprocessor.core.utils.Log;

public class VideoProcessor extends BaseProcessor {
    private static final String TAG = "VideoProcessor";

    //传入dumpDir就会开启dump， 否则不开启
    public VideoProcessor(Context context, ProcessorListener listener) {
        super(context, listener, PROCESSOR_TYPE_VIDEO);
    }

    /**
     * 异步处理文件的入口
     *
     * @param dir
     * @param outPath
     * @param token
     * @param delayNum
     * @return
     */
    @Deprecated
    public int process(final String dir, final String outPath, final String ldcPath,
                           final String token, int delayNum, int outputWidth, int outputHeight) throws RemoteException {
        if (mProcessor != null) {
            Log.i(TAG, "process " + token);
            return mProcessor.processVideo(mProcessorId, dir, outPath, ldcPath, token, delayNum, outputWidth, outputHeight);
        }
        Log.e(TAG, "process null processor");
        return PROCESS_RESULT_NULL_CLIENT_PROCESSOR;
    }


    /**
     * 使用 ProcessParams.Builder来构建Bundle
     * <p>
     * ProcessParams.Builder builder = new ProcessParams.Builder();
     * builder.setInputPath(dir).setOutputPath(outputPath).setLdcPath(ldcPath);
     * .setToken(token);
     * <p>
     * 下面是一些可选的参数
     * builder.setDelayNum(delayNum);//目前推荐设置为30
     * builder.setOutputWidth(outputWidth);//如果不设置，输出宽高就是原始视频宽高
     * builder.setOutputHeight(outputHeight);
     * Bundle bundle = builder.build();
     *
     * @param bundle
     * @return
     */
    public int process(@NonNull Bundle bundle) throws RemoteException {
        if (mProcessor != null) {
            Log.i(TAG, "process bundle " + bundle.getString(TOKEN));
            return mProcessor.processVideoBundle(mProcessorId, bundle);
        }
        Log.e(TAG, "process bundle null processor");
        return PROCESS_RESULT_NULL_CLIENT_PROCESSOR;
    }
}
