package com.xiaomi.algoprocessor.core.processor.server;

import static com.xiaomi.algoprocessor.core.processor.ProcessParams.DIR;
import static com.xiaomi.algoprocessor.core.processor.ProcessParams.LDC_PATH;
import static com.xiaomi.algoprocessor.core.processor.ProcessParams.OUTPUT_IMAGE_TYPE;
import static com.xiaomi.algoprocessor.core.processor.ProcessParams.OUT_PATH;
import static com.xiaomi.algoprocessor.core.processor.ProcessParams.TOKEN;
import static com.xiaomi.algoprocessor.core.processor.ProcessParams.WATERMARK_TYPE;

import android.content.Context;
import android.os.Bundle;
import android.os.Environment;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Size;

import androidx.annotation.AnyThread;
import androidx.annotation.Nullable;

import com.xiaomi.algoprocessor.core.data.ProcessEntry;
import com.xiaomi.algoprocessor.core.data.ProcessInput;
import com.xiaomi.algoprocessor.core.data.ProcessOutput;
import com.xiaomi.algoprocessor.core.processor.ProcessParams;
import com.xiaomi.algoprocessor.core.utils.ExifInterface;
import com.xiaomi.algoprocessor.core.utils.ExifTool;
import com.xiaomi.algoprocessor.core.utils.FileUtil;
import com.xiaomi.algoprocessor.core.utils.ImageUtil;
import com.xiaomi.algoprocessor.core.utils.Log;
import com.xiaomi.algoprocessor.core.utils.LooperHandler;
import com.xiaomi.algoprocessor.core.utils.MemoryChecker;
import com.xiaomi.algoprocessor.core.utils.WatermarkUtil;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.util.List;

public class JpegProcessorServer extends BaseProcessorServer {
    private static final String TAG = "JPServer";
    private static final int NORMAL_PICTURE = 1;
    private static final int UNREVISED_PICTURE = 2;
    private final LooperHandler mProcessHandler;
    private AlgoRunnable mProcessingRunnable;
    private Context mContext;

    //传入dumpDir就会开启dump， 否则不开启
    public JpegProcessorServer(@Nullable Context context, ProcessorListenerServer listener, int limit, @Nullable String dumpDir) {
        super(listener, dumpDir, limit);
        mContext = context.getApplicationContext();
        mProcessHandler = new LooperHandler("_process", Process.THREAD_PRIORITY_DEFAULT);
    }

    @Override
    public int init(ProcessEntry entry) {
        Log.i(TAG, "init:E");
        int result = initNative(entry);
        Log.i(TAG, "init:X " + result);
        return result;
    }

    @Override
    public int uninit(long handle) {
        Log.i(TAG, "uninit:E");
        int result = uninitNative(handle);
        Log.i(TAG, "uninit:X " + result);
        return result;
    }

    @Override
    public int process(final ProcessInput input, ProcessOutput output) {
        if (input.handle > 0) {
            return processNative(input.handle, input, output);
        }
        return ERROR_CODE_INVALID_HANDLE;
    }

    public int processBundle(final String processorId, final Bundle bundle) {
        return process(processorId, bundle.getString(DIR), bundle.getString(OUT_PATH),
                bundle.getString(LDC_PATH), bundle.getString(TOKEN), bundle.getInt(WATERMARK_TYPE),
                bundle.getString(ProcessParams.OUT_UNREVISED_PATH), bundle.getInt(OUTPUT_IMAGE_TYPE),
                bundle.getBoolean(ProcessParams.PROCESS_FIRST));
    }

    /**
     * 同步方法，直接找ev0的YUV或者heic，然后生成jpeg或者heic. 不会去调用回调接口
     * 我们希望当传入的是多帧heic的时候，要生成的image也是heic, 这样可以减少heic到jpeg的转换
     */
    public boolean genDefaultImage(final String processorId, final Bundle bundle) {
        String token = bundle.getString(TOKEN);
        ProcessEntry entry = new ProcessEntry(token);
        entry.processorId = processorId;
        entry.inputPath = bundle.getString(DIR);
        entry.outputPath = bundle.getString(OUT_PATH);
        entry.outputImageType = bundle.getInt(OUTPUT_IMAGE_TYPE);
        entry.watermarkType = bundle.getInt(WATERMARK_TYPE);
        Log.e(TAG, "genDefaultImage token " + token);
        if (checkFilePathAndType(entry.outputPath, entry.outputImageType)) {
            String defaultImg = getDesiredImagePath(entry);
            if (defaultImg == null) {
                return false;
            }

            if (!MemoryChecker.checkAppHeapMemory(new File(defaultImg).length() * 2)) {
                Log.e(TAG, "genDefaultImage:no enough memory");
                return false;
            }

            Size size = FileUtil.filterSizeFromDir(entry.inputPath);
            if (size == null) {
                Log.e(TAG, "must contain size info");
                return false;
            }
            Log.e(TAG, "genDefaultImage size " + size);
            entry.width = size.getWidth();
            entry.height = size.getHeight();

            List<String> icc = FileUtil.filterSuffix(entry.inputPath, FileUtil.SUFFIX_ICC);
            if (!icc.isEmpty()) {
                entry.iccPath = icc.get(0);
            }

            List<String> exif = FileUtil.filterSuffix(entry.inputPath, FileUtil.SUFFIX_EXIF);
            if (!exif.isEmpty()) {
                entry.exifPath = exif.get(0);
            }

            List<String> bins = FileUtil.filterFiles(entry.inputPath, FileUtil.REGEX_NAME_LDC_BIN);
            if (!bins.isEmpty()) {
                entry.ldcPath = bins.get(0);
            }

            if (entry.outputImageType == ProcessParams.OUTPUT_IMAGE_TYPE_JPEG
                    && entry.inputFormatType == ProcessEntry.INPUT_FORMAT_NV21) {
                // 输入是NV21，输出是jpeg，这种情况需要转换一下，要先增加水印和exif信息？OTA2之前都是这种情况
                ProcessOutput output = new ProcessOutput();
                output.width = entry.width;
                output.height = entry.height;
                output.result = FileUtil.readFileToByteArray(new File(defaultImg));
                processSuccess(entry, output, false);
                return true;
            } else if (entry.outputImageType == ProcessParams.OUTPUT_IMAGE_TYPE_HEIC
                    && entry.inputFormatType == ProcessEntry.INPUT_FORMAT_HEIC) {
                // 如果输入是heic，输出也是heic，这种情况先不支持，因为输出的heic加水印和exif信息的通路还没有打通
                Log.e(TAG, "genDefaultImage: invalid input heic and output heic");
                return false;
            } else if (entry.outputImageType == ProcessParams.OUTPUT_IMAGE_TYPE_JPEG
                    && entry.inputFormatType == ProcessEntry.INPUT_FORMAT_HEIC) {
                // 输入是heic，输出是jpeg，这种情况需要转换一下， OTA2之后都是这种情况
                byte[] nv21 = heicToNV21(defaultImg, entry.width, entry.height);
                if (nv21 != null && nv21.length > 0) {
                    ProcessOutput output = new ProcessOutput();
                    output.width = entry.width;
                    output.height = entry.height;
                    output.result = nv21;
                    processSuccess(entry, output, false);
                    return true;
                } else {
                    Log.e(TAG, "genDefaultImage: error heic to nv21");
                    return false;
                }
            } else if (entry.outputImageType == ProcessParams.OUTPUT_IMAGE_TYPE_HEIC
                    && entry.inputFormatType == ProcessEntry.INPUT_FORMAT_NV21) {
                // 输入是nv21，输出是heic，这种情况先不支持，因为输出的heic加水印和exif信息的通路还没有打通
                Log.e(TAG, "genDefaultImage: invalid input nv21 and output heic");
                return false;
            } else {
                Log.e(TAG, "genDefaultImage: invalid input and output type");
                return false;
            }
        }
        Log.e(TAG, "genDefaultImage: invalid output path and type");
        return false;
    }

    private String getDesiredImagePath(ProcessEntry entry) {
        List<String> files = FileUtil.filterPattern(entry.inputPath, FileUtil.NV21P);
        if (files.isEmpty()) {
            List<String> heics = FileUtil.filterPattern(entry.inputPath, FileUtil.HEICP);
            if (heics.isEmpty()) {
                Log.e(TAG, "could not get desired image path");
                return null;
            } else {
                entry.inputFormatType = ProcessEntry.INPUT_FORMAT_HEIC;
                List<String> heicStrs = FileUtil.filterPattern(entry.inputPath, FileUtil.HEICP);
                for (String s : heicStrs) {
                    if (s.contains("ev0_")) {
                        Log.d(TAG, "default image path " + s);
                        return s;
                    }
                }
            }
        } else {
            entry.inputFormatType = ProcessEntry.INPUT_FORMAT_NV21;
            List<String> nv21Strs = FileUtil.filterPattern(entry.inputPath, FileUtil.NV21P);
            for (String s : nv21Strs) {
                if (s.contains("ev0_")) {
                    Log.d(TAG, "default image path " + s);
                    return s;
                }
            }
        }
        Log.e(TAG, "could not get desired image path");
        return null;
    }

    /**
     * 异步处理入口方法
     *
     * @param dir        目录，包含NV21文件以及所有的元数据文件，相关文件必须要找命名规范来
     * @param outputPath 可以为空，如果不为空的话，只支持jpeg格式的保存
     * @param token
     * @return
     */
    @Deprecated
    public int process(final String processorId, final String dir, @Nullable String outputPath,
                       final String ldcPath, final String token, final int watermarkType) {
        return process(processorId, dir, outputPath, ldcPath, token, watermarkType, null, ProcessParams.OUTPUT_IMAGE_TYPE_JPEG, false);
    }

    public int process(final String processorId, final String dir, @Nullable String outputPath,
                       final String ldcPath, final String token, final int watermarkType,
                       final String unrevisedPath, final int outputImageType, boolean processFirst) {
        Log.i(TAG, "receive a process request " + token + ",output " + outputPath + ", input dir " + dir);
        if (!checkOutput(outputPath)) {
            Log.e(TAG, "invalid output path");
            return PROCESS_RESULT_INVALID_OUTPUT;
        }
        synchronized (this) {
            if (mLimit > 0 && mQueue.size() >= mLimit) {
                Log.e(TAG, "process： exceed limit " + mLimit);
                return PROCESS_RESULT_QUEUE_FULLED;
            }
        }

        ProcessEntry entry = new ProcessEntry(token);
        entry.processorId = processorId;
        entry.inputPath = dir;
        entry.outputPath = outputPath;
        entry.dumpPath = mDumpDir;
        entry.watermarkType = watermarkType;
        entry.unrevisedPath = unrevisedPath;
        entry.outputImageType = outputImageType;

        List<String> icc = FileUtil.filterSuffix(dir, FileUtil.SUFFIX_ICC);
        if (icc.isEmpty()) {
            Log.e(TAG, "find no icc files");
        } else if (icc.size() > 1) {
            Log.e(TAG, "find more than one icc files");
            //宽容一点，取第一个
            entry.iccPath = icc.get(0);
        } else {
            entry.iccPath = icc.get(0);
        }

        List<String> exif = FileUtil.filterSuffix(dir, FileUtil.SUFFIX_EXIF);
        if (exif.isEmpty()) {
            Log.e(TAG, "find no exif files");
        } else if (exif.size() > 1) {
            Log.e(TAG, "find more than one exif files");
            entry.exifPath = exif.get(0);
        } else {
            entry.exifPath = exif.get(0);
        }

        //只有最终路径有效的情况下，才会去check其他的路径
        if (entry.outputPath != null) {
            if (!checkFilePathAndType(entry.outputPath, entry.outputImageType)) {
                Log.e(TAG, "invalid output path and type");
                return PROCESS_RESULT_INVALID_OUTPUT;
            }
            File outputFile = new File(entry.outputPath);
            if (outputFile.exists()) {
                Log.e(TAG, "output path is already exist, you should delete if first!!!");
                return PROCESS_RESULT_INVALID_OUTPUT;
            }

            if (entry.unrevisedPath != null) {
                if (!checkFilePathAndType(entry.unrevisedPath, entry.outputImageType)) {
                    Log.e(TAG, "invalid unrevised path and type");
                    return PROCESS_RESULT_INVALID_UNREVISED;
                }
                File unrevisedFile = new File(entry.unrevisedPath);
                if (unrevisedFile.exists()) {
                    Log.e(TAG, "unrevised output path is already exist, you should delete if first!!!");
                    return PROCESS_RESULT_INVALID_UNREVISED;
                }
            }
        }

        if (ldcPath == null) {
            List<String> bins = FileUtil.filterFiles(dir, FileUtil.REGEX_NAME_LDC_BIN);
            if (bins.isEmpty()) {
                Log.e(TAG, "could not find ldc bin");
            } else if (bins.size() > 1) {
                Log.e(TAG, "find more than one ldc bin file");
                entry.ldcPath = bins.get(0);
            } else {
                entry.ldcPath = bins.get(0);
            }
        } else {
            entry.ldcPath = ldcPath;
        }
        Log.e(TAG, "find ldc bin path " + entry.ldcPath);
        if (entry.ldcPath == null) {
            return PROCESS_RESULT_INVALID_LDC;
        }

        List<String> debugBin = FileUtil.filterFiles(dir, FileUtil.REGEX_NAME_DEBUG_BIN);
        if (debugBin.size() == 1) {
            entry.debugBinPath = debugBin.get(0);
        } else {
            Log.e(TAG, "could not find 3A Debug bin");
        }

        List<String> metas = FileUtil.filterPattern(dir, FileUtil.METAP);
        if (metas.isEmpty()) {
            Log.e(TAG, "could not find meta files");
            return PROCESS_RESULT_INVALID_META;
        }

        //SN需要使用多个Meta, 这里仅仅记录一下
        if (metas.size() > 1) {
            Log.i(TAG, "mete file num " + metas.size());
        }
        entry.metaNum = metas.size();
        entry.metaPath = metas;

        List<String> gyros = FileUtil.filterFiles(dir, FileUtil.REGEX_NAME_GYRO);
        //照片处理一般不需要gyro,所以为空也可以接受
        if (!gyros.isEmpty()) {
            entry.gyroNum = gyros.size();
            entry.gyroPath = gyros;
        }

        Size size = FileUtil.filterSizeFromDir(dir);
        if (size == null) {
            Log.e(TAG, "must contain size info");
            return PROCESS_RESULT_INVALID_DIR_NAME;
        }
        Log.e(TAG, "nv21 size " + size);
        entry.width = size.getWidth();
        entry.height = size.getHeight();

        int result = PROCESS_RESULT_QUEUED;
        synchronized (this) {
            int existIndex = -1;
            for (int i = 0; i < mQueue.size(); i++) {
                ProcessEntry old = mQueue.get(i);
                if (old.processorId.equals(processorId) && old.token.equals(token)) {
                    existIndex = i;
                    break;
                }
            }
            if (processFirst) {
                if (existIndex != -1) {
                    mQueue.remove(existIndex);
                    mQueue.add(0, entry);
                    result = PROCESS_RESULT_MOVE_FIRST;
                } else {
                    mQueue.add(0, entry);
                }
            } else {
                if (existIndex == -1) {
                    mQueue.add(entry);
                } else {
                    Log.e(TAG, "error!!! process " + token + " too many times");
                    result = PROCESS_RESULT_ALREADY_EXIST;
                }
            }
            if (mQueue.size() == 1) {
                tryHandleNext();
            }
        }
        return result;
    }

    private boolean checkFilePathAndType(String path, int type) {
        if ((path.endsWith("jpeg") || path.endsWith("JPEG")) && type != ProcessParams.OUTPUT_IMAGE_TYPE_JPEG) {
            Log.e(TAG, "output file type is jpeg while output file name is not jpeg");
            return false;
        }
        if ((path.endsWith("heic") || path.endsWith("HEIC")) && type != ProcessParams.OUTPUT_IMAGE_TYPE_HEIC) {
            Log.e(TAG, "output file type is heic while output file name is not hiec");
            return false;
        }
        return true;
    }

    @AnyThread
    private void tryHandleNext() {
        Log.i(TAG, "tryHandleNext");
        synchronized (this) {
            if (mProcessingEntry != null) {
                return;
            }
            if (!mQueue.isEmpty()) {
                ProcessEntry entry = mQueue.remove(0);
                //每次处理前都要先init，获取handle
                mProcessingEntry = entry;
                sendProcessRequest(entry);
            }
        }
    }

    @AnyThread
    private void sendProcessRequest(final ProcessEntry entry) {
        AlgoRunnable runnable = new AlgoRunnable(entry);
        synchronized (this) {
            mProcessingRunnable = runnable;
        }
        mProcessHandler.post(runnable);
    }

    private void handleProcessRequest(ProcessEntry entry) {
        Log.i(TAG, "process: E");
        entry.enableAlgoDebug = mEnableAlgoDebug;
        ProcessInput input = new ProcessInput();

        List<String> files = FileUtil.filterPattern(entry.inputPath, FileUtil.NV21P);
        if (files.isEmpty()) {
            List<String> heics = FileUtil.filterPattern(entry.inputPath, FileUtil.HEICP);
            if (heics.isEmpty()) {
                Log.e(TAG, "error!!! could not find ether yuv or heic");
                synchronized (this) {
                    if (entry == mProcessingEntry) {
                        mProcessingEntry = null;
                        mProcessingRunnable = null;
                    }
                }
                tryHandleNext();
                mListener.onProcessSequenceFailed(entry.processorId, entry.token, ERROR_CODE_INVALID_INPUT);
                return;
            } else {
                Log.e(TAG, "input file is heic");
                if (!fillProcessInputInner(entry, input, heics, ProcessEntry.INPUT_FORMAT_HEIC)) {
                    return;
                }
            }
        } else {
            Log.e(TAG, "input file is yuv");
            if (!fillProcessInputInner(entry, input, files, ProcessEntry.INPUT_FORMAT_NV21)) {
                return;
            }
        }
        mListener.onProcessSequenceStarted(entry.processorId, entry.token);
        mListener.onProcessStarted(entry.processorId, entry.token);
        if (init(entry) == 0) {
            input.handle = entry.handle;

            ProcessOutput output = new ProcessOutput();
            output.handle = entry.handle;
            output.width = entry.width;
            output.height = entry.height;

            Log.i(TAG, "processNative:E");
            if (processNative(entry.handle, input, output) == 0) {
                Log.i(TAG, "processNative:X");
                if (DUMP_ENABLE && output.result != null) {
                    ImageUtil.dumpYUV(output.result, output.width, output.height,
                            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getAbsolutePath(), entry.token, "processed");
                }
                processSuccess(entry, output, true);
            } else {
                Log.i(TAG, "processNative:X failed");
                Log.i(TAG, "process: X failed");
                mListener.onProcessFailed(entry.processorId, entry.token, ERROR_CODE_PROCESS_NATIVE);
                mListener.onProcessSequenceFailed(entry.processorId, entry.token, ERROR_CODE_PROCESS_NATIVE);
            }
        } else {
            Log.e(TAG, "init error");
            mListener.onProcessFailed(entry.processorId, entry.token, ERROR_CODE_INIT_ERROR);
            mListener.onProcessSequenceFailed(entry.processorId, entry.token, ERROR_CODE_INIT_ERROR);
        }
        if (entry.handle != 0) {
            uninit(entry.handle);
            entry.handle = 0;
        }
        synchronized (this) {
            if (mProcessingEntry == entry) {
                mProcessingEntry = null;
                mProcessingRunnable = null;
            }
        }
        tryHandleNext();
    }

    private void processSuccess(ProcessEntry entry, ProcessOutput output, boolean needCallback) {
        ExifTool.ExifInfo exifInfo = null;
        if (entry.outputPath != null) {
            //TODO: 蜂巢需要传一个开关过来
            //先添加白边水印, 很消耗内存， 水印生成完毕之后，宽高也会发生变化，要更新output width&height
            if (output.result != null && entry.watermarkType != WatermarkUtil.WATERMARK_TYPE_NONE) {
                if (entry.exifPath != null) {
                    exifInfo = ExifTool.parse(entry.exifPath);
                    WatermarkUtil.addWatermark(mContext, output, exifInfo);
                } else {
                    Log.i(TAG, "error!!! add watermark for no exif");
                }
            }

            byte[] jpegData = processJpeg(entry, output, exifInfo, NORMAL_PICTURE);
            if (jpegData != null) {
                FileUtil.writeFully(jpegData, entry.outputPath);//先保存成jpeg图片吧，他们将来肯定还要对jpeg做一堆的处理

                byte[] unrevisedData = null;
                if (output.unRevisedResult != null) {
                    // entry.unrevisedPath这里理论上必须要有值，否则output.unRevisedResult 就应该为空
                    if (entry.unrevisedPath == null) {
                        Log.e(TAG, "unrevisedPath should not be null");
                    } else {
                        unrevisedData = processJpeg(entry, output, exifInfo, UNREVISED_PICTURE);
                        if (unrevisedData != null) {
                            FileUtil.writeFully(unrevisedData, entry.unrevisedPath);
                        }
                    }
                }
                if (needCallback) {
                    Log.i(TAG, "process: X");
                    mListener.onProcessCompleted(entry.processorId, output, entry.token);
                    mListener.onProcessSequenceCompleted(entry.processorId, entry.token, entry.extraResultCode);
                }
            } else {
                if (needCallback) {
                    Log.i(TAG, "processNative:X failed");
                    Log.i(TAG, "process: X failed");
                    mListener.onProcessFailed(entry.processorId, entry.token, ERROR_CODE_PROCESS_NATIVE);
                    mListener.onProcessSequenceFailed(entry.processorId, entry.token, ERROR_CODE_PROCESS_NATIVE);
                }
            }
        } else {
            if (needCallback) {
                Log.i(TAG, "process: X");
                mListener.onProcessCompleted(entry.processorId, output, entry.token);
                mListener.onProcessSequenceCompleted(entry.processorId, entry.token, entry.extraResultCode);
            }
        }
    }

    private byte[] processJpeg(ProcessEntry entry, ProcessOutput output, ExifTool.ExifInfo exifInfo, int type) {
        byte[] jpegData = null;
        byte[] imageData = type == NORMAL_PICTURE ? output.result : output.unRevisedResult;
        try {
            jpegData = FileUtil.toJpeg(imageData, output.width, output.height);
        } catch (IOException e) {
            Log.e(TAG, "error!!! toJpeg " + e.getMessage());
        }
        if (entry.debugBinPath != null && jpegData != null) {
            Log.e(TAG, "try to insert 3A debug info " + entry.debugBinPath);
            try {
                jpegData = FileUtil.insertBinIntoJpeg(jpegData, FileUtil.reformatBinFile(entry.debugBinPath));
            } catch (IOException e) {
                Log.e(TAG, "failed to insert 3A debug " + e.getMessage());
            }
        }
        ExifInterface exifInterface = null;
        if (jpegData != null) {
            try {
                exifInterface = new ExifInterface(new ByteArrayInputStream(jpegData));
            } catch (IOException e) {
                Log.e(TAG, "error!!! create exif failed");
            }
        }

        if (exifInfo != null) {
            //写入exif信息
            if (exifInterface != null) {
                ExifTool.writeExif(exifInterface, exifInfo);
            }
        } else if (entry.exifPath != null) {
            Log.e(TAG, "exif path is " + entry.exifPath);
            //写入exif信息
            if (exifInterface != null) {
                ExifTool.writeExif(exifInterface, entry.exifPath);
            }
        }
        if (entry.iccPath != null) {
            if (exifInterface != null) {
                try {
                    ExifTool.writeIcc(exifInterface, entry.iccPath);
                } catch (IOException e) {
                    Log.e(TAG, "failed to insert icc profile " + e.getMessage());
                }
            }
        }
        if (exifInterface != null) {
            jpegData = ExifTool.saveExif(exifInterface, jpegData);
        }
        return jpegData;
    }

    private boolean fillProcessInputInner(ProcessEntry entry, ProcessInput input, List<String> files, int inputType) {
        //必须确保至少要有两个image的剩余空间，因为压缩jpeg和处理exif需要用到
        if (MemoryChecker.checkAppHeapMemory(new File(files.get(0)).length() * 2)) {
            entry.inputFormatType = inputType;
            input.inputFormatType = inputType;
            input.imgNum = files.size();
            input.width = entry.width;
            input.height = entry.height;
            input.files = files;
//                List<ProcessImage> images = new ArrayList<>(input.imgNum);
//                for (String s : files) {
//                    byte[] date = FileUtil.readFileToByteArray(new File(s));
//                    ProcessImage image = new ProcessImage();
//                    image.data = date;
//                    images.add(image);
//                }
//                input.images = images;
            return true;
        } else {
            Log.e(TAG, "error!!! memory is not enough");
            synchronized (this) {
                if (entry == mProcessingEntry) {
                    mProcessingEntry = null;
                    mProcessingRunnable = null;
                }
            }
            tryHandleNext();
            mListener.onProcessSequenceFailed(entry.processorId, entry.token, ERROR_CODE_MEMORY_NOT_ENOUGH);
            return false;
        }
    }

    private boolean checkOutput(@Nullable String outputPath) {
        if (TextUtils.isEmpty(outputPath)) {
            Log.e(TAG, "null output path");
            return false;
        }
        boolean result = outputPath.endsWith(".jpg") || outputPath.endsWith(".jpeg");
        if (!result) {
            Log.e(TAG, "not support output format");
        }
        return result;
    }

    @Override
    public void quit() {
        synchronized (this) {
            if (!mQueue.isEmpty()) {
                mQueue.forEach(entry -> {
                    mListener.onProcessSequenceCanceled(entry.processorId, entry.token);
                });
            }
            mQueue.clear();
        }
        mProcessHandler.quit();
        synchronized (this) {
            if (mProcessingEntry != null) {
                if (mProcessingRunnable != null) {
                    mProcessingRunnable.waitProcessDone();
                    mProcessingRunnable = null;
                }
                if (mProcessingEntry.handle != 0) {
                    //TODO: quit可以在任意线程调用， 如果在quit时候， 算法仍然在处理， 那么此时调用uninit方法可能会出问题
                    uninit(mProcessingEntry.handle);
                    mProcessingEntry.handle = 0;
                }
                mProcessingEntry = null;
            }
        }
        mContext = null;
    }

    @Override
    public void remove(final String clientId) {
        synchronized (this) {
            mQueue.removeIf(entry -> entry.processorId.equals(clientId));
        }
    }

    @Override
    public boolean cancel(String clientId, String token) {
        boolean result = false;
        synchronized (this) {
            result = mQueue.removeIf(entry -> entry.processorId.equals(clientId) && entry.token.equals(token));
        }
        if (result) {
            mListener.onProcessSequenceCanceled(clientId, token);
        }
        return result;
    }

    private class AlgoRunnable implements Runnable {

        private volatile boolean isProcessing;
        private final ProcessEntry mEntry;

        public AlgoRunnable(ProcessEntry entry) {
            mEntry = entry;
        }

        @Override
        public void run() {
            isProcessing = true;
            handleProcessRequest(mEntry);
            isProcessing = false;
            synchronized (this) {
                this.notifyAll();
            }
        }

        public void waitProcessDone() {
            synchronized (this) {
                long now = SystemClock.elapsedRealtime();
                long end = now + 1000;
                while (isProcessing && now < end) {
                    try {
                        this.wait(end - now);
                    } catch (InterruptedException e) {
                        Log.e(TAG, "retryAndBlock interrupted " + e.getMessage());
                    }
                    now = SystemClock.elapsedRealtime();
                }
            }
        }
    }

    private native int initNative(ProcessEntry entry);

    private native int uninitNative(long handle);

    private native int processNative(long handle, ProcessInput input, ProcessOutput output);

    private static native void setupNative();

    private static native byte[] heicToNV21(String heicPath, int width, int height);

    private static native byte[] heicToI420(String heicPath, int width, int height);

    static {
        System.loadLibrary("jpeg_processor");
        setupNative();
    }
}
