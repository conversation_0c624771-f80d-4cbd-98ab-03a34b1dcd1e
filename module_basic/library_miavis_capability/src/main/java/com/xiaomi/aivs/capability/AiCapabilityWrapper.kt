@file:Suppress("<PERSON><PERSON><PERSON><PERSON>", "LongParameterList")

package com.xiaomi.aivs.capability

import android.content.Context
import androidx.annotation.Keep
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.db.bean.F2fTranslateRecord
import com.xiaomi.ai.capability.AiCapability
import com.xiaomi.ai.capability.AiConfig
import com.xiaomi.ai.capability.callback.ErrorCallback
import com.xiaomi.ai.capability.callback.SpeechRecognizeCallback
import com.xiaomi.ai.capability.callback.TtsCallback
import com.xiaomi.ai.capability.constant.Env
import com.xiaomi.ai.capability.constant.Language
import com.xiaomi.ai.capability.constant.ScenarioType
import com.xiaomi.ai.capability.model.RecognizeResult
import com.xiaomi.ai.capability.request.CheckTextInputCallback
import com.xiaomi.ai.capability.request.IRequestFactory
import com.xiaomi.ai.capability.request.ShareRequestCallback
import com.xiaomi.ai.capability.request.TransRequestCallback
import com.xiaomi.ai.capability.request.TransResultCallback
import com.xiaomi.ai.capability.request.model.CheckTextInputResponse
import com.xiaomi.ai.capability.request.model.ShareResponse
import com.xiaomi.ai.capability.request.model.TransReqResponse
import com.xiaomi.ai.capability.request.model.TransResResponse
import com.xiaomi.ai.capability.request.model.TranscribeStatusType
import com.xiaomi.ai.core.AivsConfig
import com.xiaomi.ai.log.Logger
import com.xiaomi.aivs.config.ConfigCache
import com.xiaomi.aivs.data.AudioPm
import com.xiaomi.aivs.engine.state.AudioFocusState
import com.xiaomi.aivs.player.AudioPlayer
import com.xiaomi.aivs.player.UtteranceListener
import com.xiaomi.aivs.utils.CommonUtils
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap

class AiCapabilityWrapper private constructor() :
    IAiCapability,
    SpeechRecognizeCallback,
    TtsCallback,
    ErrorCallback,
    UtteranceListener {
    private var appContext: Context = LibBaseApplication.instance

//    private val coroutineExceptionHandler = CoroutineExceptionHandler { _, exception ->
//        Timber.d("AiCapabilityWrapper Caught: $exception")
//    }
//
//    @OptIn(DelicateCoroutinesApi::class)
//    private val workScope =
//        CoroutineScope(newSingleThreadContext("AiCapabilityWrapper") + coroutineExceptionHandler)

    private val audioTrack = AudioPlayer(this)
    private var sessionId: String? = null
    private var aiCapability: AiCapability? = null
    private var callback: IAiCapabilityCallback? = null
    private var ttsPlayStartTime: Long = 0L
    private var req: IRequestFactory? = null
    private var reqId: String? = null
    private val requestOk = 200
    private val requestOrigin = "glasses"
    private var ttsEnable = true

    init {
        setLogLevel()
        initAiCapability(appContext)
    }

    private fun initAiCapability(context: Context) {
        aiCapability = AiCapability(
            context = context,
            config = buildConfig(context),
            onEvent = { code, message ->
                Timber.d("onCapabilityEvent:$code,$message")
            },
            onError = { code, message ->
                onError("", code, message)
                Timber.d("onCapabilityError:$code,$message")
            }
        )
    }

    private fun setLogLevel() {
        Logger.setLogLevel(
            if (BuildConfig.DEBUG) {
                Logger.LOG_LEVEL_DEBUG
            } else {
                Logger.LOG_LEVEL_INFO
            }
        )
    }

    fun setCallback(callback: IAiCapabilityCallback?) {
        this.callback = callback
    }

    private fun buildConfig(context: Context): AiConfig {
        return AiConfig(
            env = getEnv(),
            useInnerHost = false, // 是否使用内网访问
            clientId = DEVICE_OAUTH_CLIENT_ID,
            signSecret = DEVICE_OAUTH_SIGN_SECRET,
            apiKey = if (BuildConfig.DEBUG) {
                DEVICE_OAUTH_APP_KEY_DEBUG
            } else {
                DEVICE_OAUTH_APP_KEY_RELEASE
            },
            userAgent = CommonUtils.getUserAgent(context)
        )
    }

    private fun getEnv(): Int {
        val envCache = ConfigCache.envDomain()
        val env = when (envCache) {
            AivsConfig.ENV_PREVIEW4TEST -> Env.P4T
            AivsConfig.ENV_PRODUCTION -> Env.PROD
            else -> Env.PREV
        }
        Timber.i("getEnv envCache $envCache,env $env")
        return env
    }

    override fun startRecognizeTranslate(
        srcLang: String,
        destLang: String,
        interrupt: Boolean,
        enableTts: Boolean,
        identifyLanguage: Boolean,
        vad: Boolean,
        enableFilterSensitiveWords: Boolean
    ) {
        Timber.d("startRecognizeTranslate:$interrupt,$srcLang,$destLang,$enableTts")
        stopTts()
        aiCapability?.startRecognize(
            scenarioType = ScenarioType.GLASSES_TRANSLATION,
            interrupt = interrupt,
            srcLang = srcLang,
            destLang = destLang,
            identifyLanguage = identifyLanguage,
            vad = vad,
            enableFilterSensitiveWords = enableFilterSensitiveWords,
            recognizeCallback = this,
            ttsCallback = if (enableTts) this else null,
            errorCallback = this
        )
    }

    override fun postSpeechData(
        bytes: ByteArray,
        isFinal: Boolean
    ) {
//        Timber.d("postSpeechData:$sessionId,${bytes.size},$isFinal")
        sessionId?.let {
            aiCapability?.postData(pcm = bytes, isLastFrame = isFinal, dialogId = it)
        }
    }

    override fun stopRecognizeTranslate() {
        Timber.d("stopRecognizeTranslate:$sessionId")
        sessionId?.let {
            aiCapability?.stopRecognize(it)
            aiCapability?.reset()
            sessionId = null
        }
    }

    override fun startTts(text: String, srcLang: String, enableFilterSensitiveWords: Boolean) {
        Timber.d("startTts:$text,$srcLang,$enableFilterSensitiveWords")
        aiCapability?.startTts(
            scenarioType = ScenarioType.GLASSES_TRANSLATION,
            text = text,
            srcLang = srcLang,
            enableFilterSensitiveWords = enableFilterSensitiveWords,
            ttsCallback = this,
            errorCallback = this
        )
    }

    override fun setTtsEnable(enable: Boolean) {
        Timber.d("setTtsEnable:$enable")
        this.ttsEnable = enable
        if (!enable) {
            stopTts()
        }
    }

    override fun stopTts() {
        Timber.d("stopTts")
        audioTrack.stop("aiCapability stopTts")
    }

    override fun destroy() {
        Timber.d("destroy")
        aiCapability?.destroy()
    }

    override fun onRecognizeStart(id: String) {
        Timber.d("onRecognizeStart:$id")
        sessionId = id
    }

    override fun onRecognizeResult(id: String, result: RecognizeResult) {
        Timber.d("onRecognizeResult:$id,$result")
        callback?.onRecognizeTranslateResult(
            F2fTranslateRecord(
                id = "${id}_${result.segId}_${result.segTimestamp}",
                srcLang = result.srcLang,
                srcStr = result.srcStr,
                destLang = result.destLang,
                destStr = result.destStr,
                timestamp = result.segTimestamp
            ),
            result.isFinal
        )
    }

    /**
     * 只是识别结束的回调,非会话结束.
     */
    override fun onRecognizeStop(id: String, code: Int, reason: String) {
        Timber.d("onRecognizeStop:$id, $code, $reason")
        abnormalStopsReasonList.add(RecognizeStopEvent(id, code, reason))
    }

    override fun onPlayStart(id: String, sampleRate: Int) {
        Timber.d("onPlayStart:$id,$sampleRate,$ttsEnable,$sessionId")
        if (ttsEnable) {
            ttsPlayStartTime = System.currentTimeMillis()
            audioTrack.play()
        }
    }

    override fun onReceiveData(id: String, data: ByteArray) {
//        Timber.d("onReceiveData:$id,${data.size}")
        if (ttsEnable && !audioTrack.isPlaying()) {
            Timber.w("兼容模型只下发一次onPlayStart的case.")
            onPlayStart(id, AudioPm.SAMPLE_RATE)
        }

        if (audioTrack.isPlaying()) {
            audioTrack.onReceiveData(ttsUtteranceId(id), data)
        }
    }

    override fun onPlayFinish(id: String) {
        Timber.d("onPlayFinish:${ttsUtteranceId(id)}")
        audioTrack.onReceiveDataEnd(ttsUtteranceId(id))
    }

    override fun onError(dialogId: String, code: Int, msg: String) {
        Timber.w("onError:$dialogId,$code,$msg")
        when (code) {
            CONNECT_FAILED -> {
                Timber.w("sdk connect failed.")
                callback?.onRetry(code, msg)
            }

            NETWORK_DISABLED,
            CONNECTION_TIMEOUT,
            CONNECTION_INTERRUPT -> {
                Timber.w("sdk network error.")
                callback?.onRetry(code, msg)
            }

            TTS_LIMITED,
            ASR_TIME_OUT,
            TTS_TIME_OUT -> {
                Timber.w("sdk time out,need retry.")
                callback?.onRetry(code, msg)
            }
        }
    }

    override fun onUtteranceStart(utteranceId: String?, isUrl: Boolean, isLocalCorpus: Boolean) {
        Timber.d("onUtteranceStart:$utteranceId,$isUrl,$isLocalCorpus")
        callback?.onTTSUtteranceStart(utteranceId)
//        if (!AudioHelper.hasAudioFocus(AudioManager.AUDIOFOCUS_GAIN)) {
//            AudioHelper.requestAudioFocus(appContext, AudioManager.AUDIOFOCUS_GAIN)
//        } else {
//            Timber.w("it already has AudioFocus.")
//        }
        AudioFocusState.doFocusRequest(
            context = appContext,
            reason = "Capability_onUtteranceStart"
        )
    }

    override fun onUtteranceDone(utteranceId: String?, isUrl: Boolean, isLocalCorpus: Boolean) {
        Timber.d("onUtteranceDone:$utteranceId,$isUrl,$isLocalCorpus")
        callback?.onTTSUtteranceStop(utteranceId)
        AudioFocusState.doFocusAbandon(
            context = appContext,
            reason = "Capability_onUtteranceDone"
        )
    }

    override fun onUtteranceStop(utteranceId: String?, isUrl: Boolean) {
        Timber.d("onUtteranceStop:$utteranceId,$isUrl")
        callback?.onTTSUtteranceStop(utteranceId)
        AudioFocusState.doFocusAbandon(
            context = appContext,
            reason = "Capability_onUtteranceStop"
        )
    }

    private var transcribeListener: ((Result<TransReqResponse>) -> Unit)? = null

    fun setTranscribeListener(callback: (Result<TransReqResponse>) -> Unit) {
        transcribeListener = callback
    }

    fun fastTranscribeRequest(
        asrLanguageList: List<String> = mutableListOf(Language.ZH_CN, Language.EN_US),
        isEnableSpeakerRecognition: Boolean = false,
        filePath: String,
        onResult: (Result<TransReqResponse>) -> Unit = {}
    ) {
        Timber.i(
            "fastTranscribeRequest: isEnableSpeaker $isEnableSpeakerRecognition" +
                ",asrLanguageList $asrLanguageList"
        )
        uploadingFileMap[filePath] = true
        val callback = object : TransRequestCallback {
            override fun onResponse(resp: TransReqResponse) {
                uploadingFileMap[filePath] = false
                Timber.i("fastTranscribeRequest response $resp")
                Timber.i("fastTranscribeRequest response.code: ${resp.code}")
                when (resp.code) {
                    requestOk -> {
                        transcribeListener?.invoke(Result.success(resp))
                            ?: onResult.invoke(Result.success(resp))
                    }
                    else -> {
                        transcribeListener?.invoke(Result.failure(Exception("${resp.code}")))
                            ?: onResult.invoke(Result.failure(Exception("${resp.code}")))
                    }
                }
            }
        }
        reqId = getReqManager()?.fastTranscribeRequest(
            asrLangList = asrLanguageList,
            requestOrigin = requestOrigin,
            isEnableSpeakerRecognition = isEnableSpeakerRecognition,
            filePath = filePath,
            // 新参数代表是否对ASR结果进行润色, 眼镜暂时没这个需求, 传false即可
            isEnablePolish = false,
            callback = callback
        )
        Timber.i("fastTranscribeRequest request $reqId")
    }

    fun fetchTranscribeResult(
        taskId: String,
        token: String,
        onResult: (Result<TransResResponse>) -> Unit = {}
    ) {
        val callback = object : TransResultCallback {
            override fun onResponse(res: TransResResponse) {
                Timber.i("fetchTranscribeResult: ${res.code} $res")
                when {
                    res.code == requestOk && (res.status == TranscribeStatusType.RESULT) -> onResult(
                        Result.success(res)
                    )

                    res.code != requestOk -> onResult(Result.failure(Exception("${res.code}")))

                    else -> {
                        Timber.i("fetchTranscribeResult status: ${res.status}")
                    }
                }
            }
        }
        Timber.i("fetchTranscribeResult req $req, $token $taskId")
        getReqManager()?.fetchTranscribeResult(taskId, requestOrigin, callback)
    }

    fun transcribeShareRequest(
        transcribeText: String?,
        summaryText: String?,
        fileId: String?,
        onResult: (Result<ShareResponse>) -> Unit
    ) {
        val callback = object : ShareRequestCallback {
            override fun onResponse(res: ShareResponse) {
                Timber.i("transcribeShareRequest: ${res.code} $res ${res.requestId}")
                when {
                    res.code == requestOk -> onResult(Result.success(res))

                    res.code != requestOk -> onResult(Result.failure(Exception("${res.code}")))

                    else -> Unit
                }
            }
        }
        getReqManager()?.transcribeShareRequest(
            transcribeText = transcribeText,
            summaryText = summaryText,
            fileId = fileId,
            callback = callback
        )
    }

    fun checkInputText(
        inputText: String,
        onResult: (success: Boolean, CheckTextInputResponse) -> Unit
    ) {
        val callback = object : CheckTextInputCallback {
            override fun onResponse(res: CheckTextInputResponse) {
                Timber.i("checkInputText inputText -> $inputText, res -> $res")
                when {
                    res.code == requestOk -> onResult(true, res)

                    res.code != requestOk -> onResult(false, res)

                    else -> Unit
                }
            }
        }
        getReqManager()?.checkTextInput(
            domain = "offPersonalize",
            query = inputText,
            isLlm = false,
            toSpeak = "",
            callback = callback
        )
    }

    private fun getReqManager(): IRequestFactory? {
        req = if (null == req) {
            aiCapability?.getRequestManager()
        } else {
            req
        }
        Timber.i("getReqManager $req")
        return req
    }

    fun cancelRequest() {
        reqId?.let {
            req?.cancel(it)
        }
    }

    fun getToken(callback: ((String) -> Unit)) {
        aiCapability?.getToken { token ->
            callback.invoke(token)
        }
    }

    private fun ttsUtteranceId(dialogId: String) = "${dialogId}_$ttsPlayStartTime"

    companion object {
        const val CONNECT_FAILED = 40010006
        const val NETWORK_DISABLED = 40010007
        const val CONNECTION_INTERRUPT = 40010008
        private const val TTS_LIMITED = 50010102
        private const val ASR_TIME_OUT = 50010004
        private const val TTS_TIME_OUT = 50010005
        private const val CONNECTION_TIMEOUT = 2004
        val uploadingFileMap = ConcurrentHashMap<String, Boolean>()
        val abnormalStopsReasonList = mutableListOf<RecognizeStopEvent>()

        val INSTANCE: AiCapabilityWrapper by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            AiCapabilityWrapper()
        }
    }
}

@Keep
data class RecognizeStopEvent(
    val id: String,
    val code: Int,
    val reason: String
)
